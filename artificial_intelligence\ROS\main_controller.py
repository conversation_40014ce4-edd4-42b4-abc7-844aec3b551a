#!/usr/bin/env python
# -*- coding: UTF-8 -*-
import rospy
import math
from geometry_msgs.msg import Twist, Point, Quaternion
from nav_msgs.msg import Odometry
from std_msgs.msg import String
from sensor_msgs.msg import Image
from competition_logic_pkg.srv import GraspObject # 之前创建的服务

# 其他可能需要的导入，例如 tf.transformations
import tf
import os
import sys

# --- 开始添加的代码 ---
# 获取当前脚本文件所在的目录 (即 .../competition_ws/src/pkg/scripts)
script_dir = os.path.dirname(os.path.abspath(__file__))
# 从 scripts 目录往上一级，再进入 lib 目录
lib_dir = os.path.join(os.path.dirname(script_dir), 'lib')
# 将这个 lib 目录添加到Python的搜索路径中
sys.path.append(lib_dir)
# --- 结束添加的代码 ---

# 导入你自己的巡线算法库
from line_following_logic import LineFollowerLogic

class OdomDistanceCalculator:
    def __init__(self):
        self.prev_x = None
        self.prev_y = None
        self.total_distance = 0.0
    def update(self, x, y):
        if self.prev_x is not None and self.prev_y is not None:
            dx = x - self.prev_x
            dy = y - self.prev_y
            dist = math.sqrt(dx * dx + dy * dy)
            self.total_distance += dist
        self.prev_x = x
        self.prev_y = y
        return self.total_distance
    
class MainController:
    def __init__(self):
        rospy.init_node('main_controller', anonymous=True)

        # === 1. 核心状态与数据 ===
        self.state = 'INITIALIZING' # initializing
        self.current_pose = None
        self.start_pose = None # 用于记录某段运动的起始位置
        self.target_pose = None # 用于记录运动的目标位置
        self.target_tag_id = 2
        
        # === 2. ROS 通信接口 ===
        # 发布器
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=1) # 运动控制
        # 注意：状态发布器仍然有用，用于调试或触发其他非运动节点
        self.state_pub = rospy.Publisher('/car_state', String, queue_size=1) 

        # 订阅器
        rospy.Subscriber('/odom_to_ekf', Odometry, self.odom_callback) # 订阅里程计
        # 订阅AprilTag检测结果
        #rospy.Subscriber('/tag_detections', AprilTagDetectionArray, self.tag_callback)
        # ... 订阅其他所有识别结果的话题 ...
        # 添加开始指令订阅器
        rospy.Subscriber('/start_command', String, self.start_command_callback)

        # 服务客户端 (Service Client)
        rospy.loginfo("正在等待抓取服务 'grasp_object_service'...")
        rospy.wait_for_service('grasp_object_service') # 这会阻塞，直到服务节点启动
        try:
            self.grasp_object_client = rospy.ServiceProxy('grasp_object_service', GraspObject)
            rospy.loginfo("抓取服务已连接。")
        except rospy.ServiceException as e:
            rospy.logerr("服务连接失败:{}".format(e))
            # 在这里可以做一些错误处理，例如直接退出程序

        # === 3. 算法与逻辑模块 ===
        # 实例化巡线逻辑，但它不直接控制车辆
        self.line_follower = LineFollowerLogic()
        # 实例化里程计距离计算器
        self.calculator = OdomDistanceCalculator()

        # 你可以订阅巡线摄像头的话题，并将图像传入self.line_follower
        rospy.Subscriber('/usb_cam/image_raw', Image, self.line_cam_callback)

        # === 4. 可调参数 (从ROS参数服务器加载，便于调优) ===
        self.max_linear_speed = rospy.get_param('~max_linear_speed', 0.3) # m/s
        self.max_angular_speed = rospy.get_param('~max_angular_speed', 1.0) # rad/s
        self.goal_tolerance = rospy.get_param('~goal_tolerance', 0.05) # 5cm

        # 启动主循环
        self.state = 'WAITING_FOR_START'
        self.run()

    # --- 回调函数区 (SENSE - 感知) ---
    def start_command_callback(self, msg):
        """处理开始指令"""
        if msg.data == 'start':
            self.state = 'LINE_FOLLOWING'
            rospy.loginfo("收到开始指令，切换到循线状态")
        elif msg.data == 'stop':
            self.state = 'WAITING_FOR_START'
            self.stop_robot()
            rospy.loginfo("收到停止指令，切换到等待状态")
        elif msg.data == 'grap':
            self.state = 'GRASPING'
            rospy.loginfo("收到抓取指令，切换到抓取状态")
    def odom_callback(self, msg):
        """持续更新车辆的当前位姿"""
        self.current_pose = msg.pose.pose
        #print("里程计：当前车的位姿为：{}".format(self.current_pose))

    def tag_callback(self, msg):
        """处理AprilTag检测结果，仅在需要时触发状态切换"""
        if self.state == 'LINE_FOLLOWING' and len(msg.detections) > 0:
            # 在这里可以增加逻辑，判断是否是目标Tag ID
            # 并且可以根据Tag的姿态计算出抓取前的最佳对准位置
            self.state = 'APPROACHING_TARGET'
            # 记录下当前位置，作为对准动作的起点
            self.start_pose = self.current_pose 
            rospy.loginfo("检测到Tag，切换到[接近目标]状态。")

    def line_cam_callback(self, img_msg):
        """接收巡线图像，交给巡线逻辑库处理，但不直接发布速度"""
        # 图像处理的结果（如偏移量、角度）会保存在self.line_follower对象中
        self.line_follower.process_image(img_msg)

    # --- 辅助函数区 (TOOLS - 工具) ---
    def get_distance_to_target(self):
        """计算当前位置与目标点之间的直线距离"""
        if not self.current_pose or not self.target_pose:
            return float('inf')
        dx = self.target_pose.position.x - self.current_pose.position.x
        dy = self.target_pose.position.y - self.current_pose.position.y
        return math.sqrt(dx**2 + dy**2)

    def create_twist(self, linear_x, angular_z):
        """创建一个Twist消息，并限制最大速度"""
        twist = Twist()
        twist.linear.x = min(max(linear_x, -self.max_linear_speed), self.max_linear_speed)
        twist.angular.z = min(max(angular_z, -self.max_angular_speed), self.max_angular_speed)
        return twist

    def stop_robot(self):
        """发布一个零速指令，使机器人停止"""
        self.cmd_vel_pub.publish(self.create_twist(0, 0))
    
    def get_distance_traveled(self):
        """计算从起点开始的行驶距离"""
        if not self.current_pose:
            return 0.0
        x = self.current_pose.position.x
        y = self.current_pose.position.y
        dist = self.calculator.update(x, y)
        # rospy.loginfo("累计行走距离: %.3f 米", dist)
        return round(dist)


    # --- 主循环与状态机 (PLAN & ACT - 规划与执行) ---
    def run(self):
        rate = rospy.Rate(20) # 提高控制频率，使运动更平滑
        while not rospy.is_shutdown(): # 主循环一直运行，直到ros节点关闭
            if self.current_pose is None: # 如果没有接收到里程计信息，就每两秒输出一次”等待里程计数据“
                rospy.loginfo_throttle(2, "等待里程计数据...")
                rate.sleep()
                continue

            # 状态机核心
            self.state_machine()
            self.state_pub.publish(self.state)
            dist = self.get_distance_traveled()
            print("当前行驶距离：{}".foamat(dist))
            rate.sleep()

    def state_machine(self):
        """核心状态机，根据当前状态决定要执行的动作"""
        twist_msg = self.create_twist(0, 0) # 默认停止

        if self.state == 'WAITING_FOR_START': # waiting_for_start
            # 等待发令，保持不动
            pass
        
        elif self.state == 'LINE_FOLLOWING': # line_following
            # 从巡线逻辑库获取计算出的速度和转角
            linear_x, angular_z = self.line_follower.get_control_command()
            twist_msg = self.create_twist(linear_x, angular_z)

        elif self.state == 'PERFORMING_MANEUVER': # performing_maneuver
            # 这是一个通用的、基于里程计的精确移动状态
            distance_to_go = self.get_distance_to_target()
            if distance_to_go > self.goal_tolerance:
                # 在这里可以实现一个简单的P控制器，使移动更平滑
                # 例如，根据到目标的距离和角度差来计算速度
                # ... (计算 linear_x, angular_z) ...
                twist_msg = self.create_twist(0.1, 0) # 简化示例：缓慢前进
            else:
                rospy.loginfo("机动动作完成！")
                self.stop_robot()
                # 在这里切换到下一个状态，例如 'GRASPING' 或 'LINE_FOLLOWING'
                # self.state = 'NEXT_STATE' 
                pass

        elif self.state == 'APPROACHING_TARGET': # approaching_target
            # 这是一个更复杂的状态，可能需要结合视觉和里程计
            # 例如，使用视觉伺服来对准Tag，同时用里程计监控距离
            # ... (计算 linear_x, angular_z) ...
            # 如果对准完成，则切换状态
            # if alignment_is_good:
            #     self.state = 'GRASPING'
            pass

        elif self.state == 'GRASPING': # grasping
            # 这是一个瞬时动作，调用服务，期间机器人应保持不动
            self.stop_robot()
            rospy.loginfo("请求抓取服务，目标Tag ID: {}".format(self.target_tag_id))
            try:
                # 2. 调用服务并等待响应
                response = self.grasp_object_client(self.target_tag_id)
                
                # 3. 根据响应结果切换到下一个状态
                if response.success:
                    rospy.loginfo("服务报告: {}".format(response.message))
                    self.state = 'DELIVERING_ITEM' # 切换到送货状态
                else:
                    rospy.logwarn("服务报告: {}".format(response.message))
                    self.state = 'GRASP_FAILED' # 切换到失败处理状态
                    
            except rospy.ServiceException as e:
                rospy.logerr("调用服务时发生错误: {}".format(e))
                self.state = 'GRASP_FAILED'
                
        # 将最终计算出的速度指令发布出去
        print(twist_msg)
        self.cmd_vel_pub.publish(twist_msg)

if __name__ == '__main__':
    try:
        MainController()
    except rospy.ROSInterruptException:
        pass