#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单火焰检测测试程序
快速测试火焰检测功能，在图片上标出火焰位置
"""

import cv2
import numpy as np
import os

class SimpleFireTest:
    def __init__(self):
        """初始化简单火焰检测测试"""
        # 火焰检测参数（HSV颜色空间）
        self.fire_lower1 = np.array([0, 50, 50])      # 红色范围1
        self.fire_upper1 = np.array([10, 255, 255])
        self.fire_lower2 = np.array([170, 50, 50])    # 红色范围2  
        self.fire_upper2 = np.array([180, 255, 255])
        
        # 橙色/黄色火焰
        self.fire_lower3 = np.array([10, 50, 50])     # 橙色范围
        self.fire_upper3 = np.array([25, 255, 255])
        
        self.min_area = 200  # 最小火焰面积
        
        print("简单火焰检测测试程序已初始化")
    
    def detect_fire(self, image):
        """检测火焰区域"""
        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 创建火焰颜色掩码
        mask1 = cv2.inRange(hsv, self.fire_lower1, self.fire_upper1)  # 红色1
        mask2 = cv2.inRange(hsv, self.fire_lower2, self.fire_upper2)  # 红色2
        mask3 = cv2.inRange(hsv, self.fire_lower3, self.fire_upper3)  # 橙色
        
        # 合并所有掩码
        fire_mask = cv2.bitwise_or(mask1, mask2)
        fire_mask = cv2.bitwise_or(fire_mask, mask3)
        
        # 形态学操作去噪
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_CLOSE, kernel)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(fire_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        fire_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                # 获取边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 计算中心点
                center_x = x + w // 2
                center_y = y + h // 2
                
                fire_regions.append({
                    'contour': contour,
                    'bbox': (x, y, w, h),
                    'center': (center_x, center_y),
                    'area': area
                })
        
        return fire_regions, fire_mask
    
    def get_floor_number(self, center_y, image_height, total_floors=5):
        """根据Y坐标计算楼层"""
        floor_height = image_height / total_floors
        floor = total_floors - int(center_y / floor_height)
        return max(1, min(total_floors, floor))
    
    def draw_results(self, image, fire_regions):
        """在图像上绘制检测结果"""
        result_img = image.copy()
        
        for i, region in enumerate(fire_regions):
            # 绘制火焰轮廓
            cv2.drawContours(result_img, [region['contour']], -1, (0, 255, 255), 2)
            
            # 绘制边界框
            x, y, w, h = region['bbox']
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 0, 255), 2)
            
            # 绘制中心点
            center = region['center']
            cv2.circle(result_img, center, 5, (255, 0, 0), -1)
            
            # 计算楼层
            floor = self.get_floor_number(center[1], image.shape[0])
            
            # 添加标签
            label = f"Fire-{i+1}: {floor}F"
            cv2.putText(result_img, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # 添加面积信息
            area_label = f"Area: {region['area']:.0f}"
            cv2.putText(result_img, area_label, (x, y + h + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        return result_img
    
    def test_image(self, image_path):
        """测试单张图像"""
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"错误: 图像文件不存在 - {image_path}")
            return
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法加载图像 - {image_path}")
            return
        
        print(f"正在处理图像: {image_path}")
        print(f"图像尺寸: {image.shape[1]}x{image.shape[0]}")
        
        # 检测火焰
        fire_regions, fire_mask = self.detect_fire(image)
        
        # 绘制结果
        result_image = self.draw_results(image, fire_regions)
        
        # 显示结果
        print(f"\n检测到 {len(fire_regions)} 处火焰:")
        
        if fire_regions:
            for i, region in enumerate(fire_regions):
                floor = self.get_floor_number(region['center'][1], image.shape[0])
                print(f"  火焰 {i+1}: {floor}楼, 中心位置({region['center'][0]}, {region['center'][1]}), 面积: {region['area']:.0f}")
            
            # 找到最大的火焰区域
            main_fire = max(fire_regions, key=lambda x: x['area'])
            main_floor = self.get_floor_number(main_fire['center'][1], image.shape[0])
            print(f"\n主要火灾位置: {main_floor}楼")
        else:
            print("  未检测到火焰")
        
        # 创建显示窗口
        cv2.namedWindow('Original Image', cv2.WINDOW_NORMAL)
        cv2.namedWindow('Fire Detection Result', cv2.WINDOW_NORMAL)
        cv2.namedWindow('Fire Mask', cv2.WINDOW_NORMAL)
        
        # 调整窗口大小
        cv2.resizeWindow('Original Image', 600, 400)
        cv2.resizeWindow('Fire Detection Result', 600, 400)
        cv2.resizeWindow('Fire Mask', 600, 400)
        
        # 显示图像
        cv2.imshow('Original Image', image)
        cv2.imshow('Fire Detection Result', result_image)
        cv2.imshow('Fire Mask', fire_mask)
        
        print("\n按任意键继续，按 's' 保存结果图像...")
        key = cv2.waitKey(0) & 0xFF
        
        # 保存结果
        if key == ord('s'):
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            result_path = f"{base_name}_fire_result.jpg"
            mask_path = f"{base_name}_fire_mask.jpg"
            
            cv2.imwrite(result_path, result_image)
            cv2.imwrite(mask_path, fire_mask)
            print(f"结果已保存:")
            print(f"  检测结果: {result_path}")
            print(f"  火焰掩码: {mask_path}")
        
        cv2.destroyAllWindows()
        return fire_regions
    
    def create_demo_image(self):
        """创建演示图像"""
        # 创建白色背景
        img = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # 绘制楼宇主体
        building_color = (139, 69, 19)  # 棕色
        cv2.rectangle(img, (200, 100), (440, 400), building_color, -1)
        
        # 绘制楼层分界线
        for i in range(1, 5):
            y = 100 + i * 60
            cv2.line(img, (200, y), (440, y), (0, 0, 0), 2)
        
        # 绘制窗户
        window_color = (173, 216, 230)  # 浅蓝色
        for floor in range(5):
            for window in range(4):
                x1 = 220 + window * 50
                y1 = 110 + floor * 60
                x2 = x1 + 30
                y2 = y1 + 40
                cv2.rectangle(img, (x1, y1), (x2, y2), window_color, -1)
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 0), 1)
        
        # 添加楼宇标题
        cv2.putText(img, "Test Building", (220, 80), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 添加火焰（在第2层）
        fire_center = (270, 230)  # 第2层位置
        
        # 绘制多个火焰点模拟真实火焰
        fire_points = [
            (270, 230, 18),  # (x, y, radius)
            (280, 225, 15),
            (260, 235, 12),
            (285, 240, 10),
            (275, 220, 8)
        ]
        
        for x, y, r in fire_points:
            # 红色火焰核心
            cv2.circle(img, (x, y), r, (0, 69, 255), -1)
            # 橙色火焰外围
            cv2.circle(img, (x, y), r+3, (0, 140, 255), 2)
        
        # 添加楼层标签
        for floor in range(5):
            y_pos = 130 + floor * 60
            cv2.putText(img, f"{5-floor}F", (180, y_pos), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        return img

def main():
    """主函数"""
    tester = SimpleFireTest()
    
    print("简单火焰检测测试程序")
    print("1. 输入图像路径进行测试")
    print("2. 输入 'demo' 创建并测试演示图像")
    print("3. 输入 'quit' 退出程序")
    
    while True:
        choice = input("\n请输入选择: ").strip()
        
        if choice.lower() == 'quit':
            break
        elif choice.lower() == 'demo':
            # 创建演示图像
            demo_img = tester.create_demo_image()
            demo_path = "demo_fire_test.jpg"
            cv2.imwrite(demo_path, demo_img)
            print(f"演示图像已创建: {demo_path}")
            
            # 测试演示图像
            tester.test_image(demo_path)
        else:
            # 测试用户指定的图像
            if os.path.exists(choice):
                tester.test_image(choice)
            else:
                print(f"文件不存在: {choice}")

if __name__ == '__main__':
    main()
