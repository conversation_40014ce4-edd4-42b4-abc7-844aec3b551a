#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于YOLOv8的火焰检测测试程序
使用训练好的YOLOv8模型进行火焰检测，并在图片上标出火焰位置
"""

import cv2
import numpy as np
import os
import json
from ultralytics import YOL<PERSON>
from paddleocr import PaddleOCR

class YOLOFireDetectionTest:
    def __init__(self, model_path=r"D:\\Python\\artificial_intelligence\\ROS\\fire.pt"):
        """初始化YOLOv8火焰检测测试"""
        # 加载YOLOv8火焰检测模型
        try:
            self.fire_model = YOLO(model_path)
            print(f"✓ 成功加载YOLOv8火焰检测模型: {model_path}")
        except Exception as e:
            print(f"✗ 加载YOLOv8模型失败: {e}")
            self.fire_model = None
            return
        
        # 初始化PaddleOCR用于文字识别
        try:
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            print("✓ PaddleOCR初始化成功")
        except Exception as e:
            print(f"✗ PaddleOCR初始化失败: {e}")
            self.ocr = None
        
        # 检测参数
        self.confidence_threshold = 0.5
        self.default_floors = 5

        # 校准数据
        self.calibration_data = None

        print("YOLOv8火焰检测测试程序已初始化")

    def load_calibration_data(self, calibration_file):
        """加载楼层校准数据"""
        try:
            with open(calibration_file, 'r', encoding='utf-8') as f:
                self.calibration_data = json.load(f)
            print(f"已加载校准数据: {calibration_file}")
            return True
        except Exception as e:
            print(f"加载校准数据失败: {e}")
            return False

    def determine_floor_with_calibration(self, fire_center_y, image_path=None):
        """使用校准数据确定楼层"""
        if self.calibration_data is None:
            return None

        floor_lines = self.calibration_data.get('floor_lines', [])
        if not floor_lines:
            return None

        # 找到火焰位置在哪两条线之间
        floor_lines_sorted = sorted(floor_lines)

        for i in range(len(floor_lines_sorted) - 1):
            if floor_lines_sorted[i] <= fire_center_y <= floor_lines_sorted[i + 1]:
                # 楼层从下往上数
                floor_number = len(floor_lines_sorted) - i - 1
                return max(1, floor_number)

        # 如果在最上面或最下面
        if fire_center_y < floor_lines_sorted[0]:
            return len(floor_lines_sorted)  # 最高层
        elif fire_center_y > floor_lines_sorted[-1]:
            return 1  # 最低层

        return None

    def detect_building_name(self, image):
        """使用PaddleOCR检测楼宇名称"""
        if self.ocr is None:
            return "未知楼宇", []
        
        try:
            result = self.ocr.ocr(image, cls=True)
            
            building_name = ""
            max_confidence = 0
            text_boxes = []
            
            if result and result[0]:
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    box = line[0]
                    
                    text_boxes.append({
                        'text': text,
                        'confidence': confidence,
                        'box': box
                    })
                    
                    # 查找包含楼宇相关关键词的文本
                    building_keywords = ['楼', '大厦', '中心', '广场', '大楼', '建筑', '塔', '院']
                    if any(keyword in text for keyword in building_keywords):
                        if confidence > max_confidence:
                            max_confidence = confidence
                            building_name = text
                    
                    # 如果没有找到楼宇关键词，选择置信度最高的文本
                    if not building_name and confidence > max_confidence:
                        max_confidence = confidence
                        building_name = text
            
            return building_name if building_name else "未知楼宇", text_boxes
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return "未知楼宇", []
    
    def detect_fire_with_yolo(self, image):
        """使用YOLOv8模型检测火焰"""
        if self.fire_model is None:
            print("YOLOv8模型未加载")
            return []
        
        try:
            # 使用YOLOv8进行推理
            results = self.fire_model(image, conf=self.confidence_threshold, verbose=False)
            
            fire_detections = []
            
            # 处理检测结果
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标 (x1, y1, x2, y2)
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                        
                        # 计算中心点和尺寸
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        width = x2 - x1
                        height = y2 - y1
                        area = width * height
                        
                        # 获取置信度
                        confidence = float(box.conf[0].cpu().numpy())
                        
                        # 获取类别ID（如果模型有多个类别）
                        class_id = int(box.cls[0].cpu().numpy()) if box.cls is not None else 0
                        
                        fire_detections.append({
                            'bbox': (x1, y1, x2, y2),
                            'center': (center_x, center_y),
                            'size': (width, height),
                            'area': area,
                            'confidence': confidence,
                            'class_id': class_id
                        })
            
            return fire_detections
            
        except Exception as e:
            print(f"YOLOv8火焰检测失败: {e}")
            return []
    
    def detect_building_region(self, image):
        """检测楼宇区域"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测找到楼宇轮廓
            edges = cv2.Canny(gray, 50, 150)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # 找到最大的矩形轮廓（假设是楼宇）
            largest_contour = max(contours, key=cv2.contourArea)

            # 获取边界框
            x, y, w, h = cv2.boundingRect(largest_contour)

            # 过滤太小的区域
            if w * h < image.shape[0] * image.shape[1] * 0.1:  # 至少占图像10%
                return None

            return (x, y, w, h)

        except Exception as e:
            print(f"楼宇区域检测失败: {e}")
            return None

    def detect_floor_lines(self, image, building_bbox=None):
        """检测楼层分界线"""
        try:
            # 如果有楼宇边界框，只在楼宇区域内检测
            if building_bbox:
                x, y, w, h = building_bbox
                roi = image[y:y+h, x:x+w]
            else:
                roi = image
                x, y = 0, 0

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 检测水平边缘
            edges = cv2.Canny(gray, 50, 150)

            # 使用霍夫变换检测直线
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=int(roi.shape[1]*0.3))

            horizontal_lines = []
            if lines is not None:
                for rho, theta in lines[:, 0]:
                    # 筛选水平线（角度接近0或π）
                    if abs(theta) < 0.2 or abs(theta - np.pi) < 0.2:
                        # 计算y坐标
                        y_line = int(rho / np.sin(theta)) if abs(np.sin(theta)) > 0.1 else None
                        if y_line is not None and 0 < y_line < roi.shape[0]:
                            horizontal_lines.append(y + y_line)  # 转换回原图坐标

            return sorted(horizontal_lines)

        except Exception as e:
            print(f"楼层线检测失败: {e}")
            return []

    def determine_floor(self, fire_center_y, image_height, image=None, floors=None):
        """根据火焰Y坐标确定楼层（改进版）"""
        if floors is None:
            floors = self.default_floors

        # 方法0: 优先使用校准数据
        if self.calibration_data is not None:
            calibrated_floor = self.determine_floor_with_calibration(fire_center_y)
            if calibrated_floor is not None:
                return calibrated_floor

        # 方法1: 尝试检测楼宇区域和楼层线
        if image is not None:
            building_bbox = self.detect_building_region(image)
            floor_lines = self.detect_floor_lines(image, building_bbox)

            # 如果检测到楼层线，使用楼层线来确定楼层
            if len(floor_lines) >= 2:
                floor_lines.sort()

                # 找到火焰位置在哪两条线之间
                for i in range(len(floor_lines) - 1):
                    if floor_lines[i] <= fire_center_y <= floor_lines[i + 1]:
                        # 楼层从下往上数
                        floor_number = len(floor_lines) - i - 1
                        return max(1, floor_number)

                # 如果在最上面或最下面
                if fire_center_y < floor_lines[0]:
                    return len(floor_lines)  # 最高层
                elif fire_center_y > floor_lines[-1]:
                    return 1  # 最低层

            # 如果检测到楼宇区域，基于楼宇区域计算
            if building_bbox:
                bx, by, bw, bh = building_bbox

                # 检查火焰是否在楼宇区域内
                if by <= fire_center_y <= by + bh:
                    # 相对于楼宇区域的位置
                    relative_y = fire_center_y - by
                    floor_height = bh / floors
                    floor_number = floors - int(relative_y / floor_height)
                    return max(1, min(floors, floor_number))

        # 方法2: 回退到原始算法，但调整参数
        # 假设楼宇占图像的中下部分（60%-90%的高度区域）
        building_start_y = int(image_height * 0.1)  # 楼宇开始位置
        building_end_y = int(image_height * 0.9)    # 楼宇结束位置
        building_height = building_end_y - building_start_y

        # 如果火焰在楼宇区域内
        if building_start_y <= fire_center_y <= building_end_y:
            relative_y = fire_center_y - building_start_y
            floor_height = building_height / floors
            floor_number = floors - int(relative_y / floor_height)
            return max(1, min(floors, floor_number))

        # 如果火焰在楼宇区域外，返回最接近的楼层
        if fire_center_y < building_start_y:
            return floors  # 最高层
        else:
            return 1  # 最低层
    
    def draw_detections(self, image, fire_detections, building_name="", text_boxes=None):
        """在图像上绘制检测结果"""
        result_img = image.copy()
        
        # 绘制OCR识别的文本框
        if text_boxes:
            for text_box in text_boxes:
                box = np.array(text_box['box'], dtype=np.int32)
                cv2.polylines(result_img, [box], True, (0, 255, 0), 2)
                
                # 添加文本标签
                text = text_box['text']
                confidence = text_box['confidence']
                label = f"{text} ({confidence:.2f})"
                cv2.putText(result_img, label, tuple(box[0]), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 绘制火焰检测结果
        for i, detection in enumerate(fire_detections):
            x1, y1, x2, y2 = detection['bbox']
            center_x, center_y = detection['center']
            confidence = detection['confidence']
            
            # 计算楼层
            floor = self.determine_floor(center_y, image.shape[0], image)
            
            # 绘制边界框
            cv2.rectangle(result_img, (x1, y1), (x2, y2), (0, 0, 255), 3)
            
            # 绘制中心点
            cv2.circle(result_img, (center_x, center_y), 5, (255, 0, 0), -1)
            
            # 添加标签
            label = f"Fire-{i+1}: {floor}F"
            confidence_label = f"Conf: {confidence:.2f}"
            
            # 绘制标签背景
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            cv2.rectangle(result_img, (x1, y1-35), (x1 + label_size[0] + 10, y1), (0, 0, 255), -1)
            
            # 绘制文字
            cv2.putText(result_img, label, (x1 + 5, y1-20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(result_img, confidence_label, (x1 + 5, y1-5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 添加楼宇名称到图像顶部
        if building_name:
            cv2.putText(result_img, f"Building: {building_name}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
        
        return result_img
    
    def test_image(self, image_path, save_result=True, show_result=True):
        """测试单张图像"""
        if not os.path.exists(image_path):
            print(f"错误: 图像文件不存在 - {image_path}")
            return None
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法加载图像 - {image_path}")
            return None
        
        print(f"\n正在处理图像: {os.path.basename(image_path)}")
        print(f"图像尺寸: {image.shape[1]}x{image.shape[0]}")

        # 0. 尝试加载校准数据
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        calibration_file = f"{base_name}_floor_calibration.json"
        if os.path.exists(calibration_file):
            if self.load_calibration_data(calibration_file):
                print("✓ 使用校准数据进行楼层检测")
            else:
                print("✗ 校准数据加载失败，使用默认算法")
        else:
            print("未找到校准数据，使用默认算法")

        # 1. 识别楼宇名称
        building_name, text_boxes = self.detect_building_name(image)
        print(f"识别到楼宇名称: {building_name}")
        
        # 2. 检测火焰
        fire_detections = self.detect_fire_with_yolo(image)
        print(f"检测到 {len(fire_detections)} 处火焰")
        
        # 3. 生成检测报告
        if fire_detections:
            print("\n火焰检测详情:")
            for i, detection in enumerate(fire_detections):
                floor = self.determine_floor(detection['center'][1], image.shape[0], image)
                confidence = detection['confidence']
                area = detection['area']
                print(f"  火焰 {i+1}: {floor}楼, 置信度: {confidence:.3f}, 面积: {area}像素")

            # 找到置信度最高的火焰
            main_fire = max(fire_detections, key=lambda x: x['confidence'])
            main_floor = self.determine_floor(main_fire['center'][1], image.shape[0], image)
            result_message = f"{building_name} {main_floor}楼发现火灾"
        else:
            result_message = f"{building_name} 未发现火灾"
        
        print(f"\n最终结果: {result_message}")
        
        # 4. 绘制检测结果
        result_image = self.draw_detections(image, fire_detections, building_name, text_boxes)
        
        # 5. 显示结果
        if show_result:
            # 创建显示窗口
            cv2.namedWindow('Original Image', cv2.WINDOW_NORMAL)
            cv2.namedWindow('YOLO Fire Detection Result', cv2.WINDOW_NORMAL)
            
            # 调整窗口大小
            cv2.resizeWindow('Original Image', 800, 600)
            cv2.resizeWindow('YOLO Fire Detection Result', 800, 600)
            
            # 显示图像
            cv2.imshow('Original Image', image)
            cv2.imshow('YOLO Fire Detection Result', result_image)
            
            print("\n按任意键继续，按 's' 保存结果...")
            key = cv2.waitKey(0) & 0xFF
            
            if key == ord('s'):
                save_result = True
            
            cv2.destroyAllWindows()
        
        # 6. 保存结果
        if save_result:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            result_path = f"{base_name}_yolo_fire_result.jpg"
            
            cv2.imwrite(result_path, result_image)
            print(f"结果已保存到: {result_path}")
        
        return {
            'building_name': building_name,
            'fire_detections': fire_detections,
            'result_message': result_message,
            'result_image': result_image
        }

def main():
    """主函数"""
    print("YOLOv8火焰检测测试程序")
    print("=" * 50)
    
    # 检查模型文件是否存在
    model_path = "D:\\Python\\artificial_intelligence\ROS\\fire_detection\model\\fire.pt"
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在 - {model_path}")
        print("请确保模型文件位于正确路径")
        return
    
    # 初始化测试器
    tester = YOLOFireDetectionTest(model_path)
    
    if tester.fire_model is None:
        print("模型加载失败，程序退出")
        return
    
    print("\n使用说明:")
    print("1. 输入图像文件路径进行测试")
    print("2. 输入 'demo' 创建演示图像并测试")
    print("3. 输入 'quit' 退出程序")
    
    while True:
        choice = input("\n请输入选择: ").strip()
        
        if choice.lower() == 'quit':
            break
        elif choice.lower() == 'demo':
            # 创建演示图像（这里可以使用您提供的楼宇图像）
            print("请提供一张包含楼宇和火焰的测试图像路径")
            demo_path = input("演示图像路径: ").strip()
            if os.path.exists(demo_path):
                tester.test_image(demo_path)
            else:
                print("演示图像不存在")
        else:
            if os.path.exists(choice):
                tester.test_image(choice)
            else:
                print(f"文件不存在: {choice}")

if __name__ == '__main__':
    main()
