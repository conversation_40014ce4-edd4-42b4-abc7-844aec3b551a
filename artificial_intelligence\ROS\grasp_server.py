#!/usr/bin/env python
# -*- coding: UTF-8 -*-
#根据数据类型import，记得加.msg

# 导入你新生成的服务类！
from competition_logic_pkg.srv import GraspObject, GraspObjectResponse

import transforms3d as tfs
import time
import tf
from geometry_msgs.msg import TransformStamped
import tf2_ros.transform_broadcaster
import math   
import rospy, sys
import moveit_commander
from moveit_msgs.msg import RobotTrajectory
from trajectory_msgs.msg import JointTrajectoryPoint
from sensor_msgs.msg import Image
import numpy as np
from geometry_msgs.msg import PoseStamped, Pose
from std_msgs.msg import String
from tf.transformations import euler_from_quaternion, quaternion_from_euler
from sensor_msgs.msg import Image
from cv_bridge import CvBridge,CvBridgeError
import cv2
from mirobot_urdf_2.srv import *

# 全局初始化，例如机械臂和场景
#  初始化MoveIt!系统，这是ROS中用于机器人运动规划的框架
moveit_commander.roscpp_initialize(sys.argv)
arm = None
listener = None


def perform_grasp_logic(target_tag_id):
    """
    这个函数包含了原来zhuaqu.py中的核心抓取逻辑。
    它现在返回一个元组 (bool, string) 代表成功与否和消息。
    """
    plan_success = True
    rospy.loginfo("开始执行针对Tag ID: {} 的抓取逻辑...".format(target_tag_id))
    
    # 1. 寻找和定位目标Tag (这部分逻辑可能需要重写)
    #    你可能需要临时订阅AprilTag话题，找到目标ID的Tag，获取其位姿。
    #    或者，如果大脑控制器已经提供了位姿，就直接使用。
    # 2. 调用MoveIt进行路径规划和执行
    #    arm.set_pose_target(...)
    #    plan_success, plan, _, _ = arm.go(wait=True)
    # 3. 控制夹爪闭合
    # 4. 根据执行结果判断是否成功

    # AprilTag检测和姿态获取
    get_pose = False
    # 创建一个PoseStamped对象，用于存储AprilTag的姿态信息
    posestamped=PoseStamped()
    posestamped.header.frame_id = 'camera_color_optical_frame'
    # 在循环中等待并获取相机坐标系到AprilTag坐标系(tag_0)的变换关系
    while not rospy.is_shutdown() and not get_pose:
        try:
            # 获取当前ros系统的时间，使用当前时间戳来查询相机坐标系到AprilTag坐标系的最新变换关系。
            now = rospy.Time.now()
            # 等待，直到TF系统能够提供从相机坐标系 (camera_rgb_optical_frame) 到AprilTag坐标系 (tag_0) 的变换关系。
            listener.waitForTransform("camera_color_optical_frame", "tag_"+target_tag_id, now, rospy.Duration(0.3))
            # 一旦变换可用，就获取 tag_0 相对于相机的位置（trans）和方向（rot，以四元数表示）。
            (trans, rot) = listener.lookupTransform("camera_color_optical_frame", "tag_"+target_tag_id, now)

        
            # 姿态处理和坐标变换
            # 姿态数据存储
            # 将通过TF系统获取到的AprilTag的平移(trans)和旋转(rot)数据存储到posestamped对象中
            # PoseStamped是ROS中表示三维空间姿态的标准消息类型。
            print(trans[0], trans[1], trans[2], rot[0], rot[1], rot[2], rot[3])
            posestamped.pose.position.x=trans[0]
            posestamped.pose.position.y=trans[1]
            posestamped.pose.position.z=trans[2]
            posestamped.pose.orientation.x=rot[0]
            posestamped.pose.orientation.y=rot[1]
            posestamped.pose.orientation.z=rot[2]
            posestamped.pose.orientation.w=rot[3]

            # 计算抓取方向，这部分代码是关键的抓取方向调整逻辑
            # m1: 创建一个绕X轴旋转180度的旋转矩阵
            # m2: 将AprilTag的四元数方向转换为旋转矩阵
            # rot2: 通过矩阵相乘计算最终的抓取方向
            # 为什么要旋转180度？
            # 因为AprilTag标记的正面朝向是向外的，而机械臂的抓取器需要从相反方向接近才能抓取物体，所以需要将方向调整180度。
            m1=tfs.euler.euler2mat(math.radians(180),math.radians(0),math.radians(0))
            # 将从AprilTag得到的原始方向（四元数）转换为旋转矩阵。
            m2=tfs.quaternions.quat2mat([rot[3],rot[0],rot[1],rot[2]])
            rot2=tfs.quaternions.mat2quat(np.dot(m2,m1)[0:3,0:3])

            # 坐标变换广播。这部分代码创建并广播一个新的坐标变换t2：
            m2=tf.TransformBroadcaster() # 创建一个TransformBroadcaster对象用于广播坐标变换
            t2 = TransformStamped() # 创建一个TransformStamped消息来描述变换关系
            # 设置父坐标系为camera_rgb_optical_frame，子坐标系为t2
            t2.header.frame_id = 'camera_color_optical_frame' 
            t2.header.stamp = rospy.Time(0)
            t2.child_frame_id = 't2'
            # 设置变换的平移部分为AprilTag的位置
            t2.transform.translation = posestamped.pose.position
            t2.transform.rotation.w=rot2[0]
            t2.transform.rotation.x=rot2[1]
            t2.transform.rotation.y=rot2[2]
            t2.transform.rotation.z=rot2[3]
            t2.transform.rotation=posestamped.pose.orientation # 这一行代码会覆盖之前对旋转部分的设置，这意味着实际使用的仍然是原始的AprilTag方向，而不是调整后的方向。
            #posestamped.pose.orientation=t2.transform.rotation
            rate = rospy.Rate(1)
            # 通过sendTransformMessage广播这个变换
            m2.sendTransformMessage(t2)

            get_pose = True

        # 如果在TF变换过程中出现任何异常（如找不到坐标变换、连接错误等），则打印错误信息并继续尝试。
        except (tf.Exception, tf.LookupException, tf.ConnectivityException):
            print("tf echo error")
            continue


    get_pose = False
    # 坐标系转换
    while not rospy.is_shutdown() and not get_pose:
        """
        将目标的姿态从相机的视角转换到机器人的视角。
        将AprilTag在相机坐标系中的姿态转换为机器人基座坐标系(/base)中的姿态
        这样机械臂就能根据自己的基座坐标系来理解目标位置
        """
        try:
            # now = rospy.Time.now() - rospy.Duration(5.0)
            now = rospy.Time.now()
            listener.waitForTransform("/base", "/camera_color_optical_frame", now, rospy.Duration(0.5))
            # 它调用TF系统，将之前在相机坐标系中定义的目标姿态 posestamped，自动转换到机器人基座坐标系 /base 中。
            pose_stamped_return = listener.transformPose("/base", posestamped)
            print (pose_stamped_return)
            m2.sendTransformMessage(t2)

            get_pose = True
        except (tf.Exception, tf.LookupException, tf.ConnectivityException):
            print("坐标系转换：tf echo error")
            continue
        

        # 机械臂控制和抓取
        # 设置目标姿态
        target_pose=pose_stamped_return
        target_pose.header.stamp = rospy.Time.now()   
        arm.set_start_state_to_current_state()

        rospy.sleep(1)
        target_pose.pose.position.x=target_pose.pose.position.x-0.07
        target_pose.pose.position.y=target_pose.pose.position.y+0.004
        target_pose.pose.position.z=target_pose.pose.position.z+0.003
        arm.set_pose_target(target_pose) # 设置目标
        arm.go(wait=True)

        rospy.sleep(1)
        target_pose.pose.position.x=target_pose.pose.position.x+0.025
        target_pose.pose.position.y=target_pose.pose.position.y
        target_pose.pose.position.z=target_pose.pose.position.z
        arm.set_pose_target(target_pose)
        arm.go(wait=True)

        # 创建一个服务客户端
        rospy.wait_for_service("switch_pump_status")
        # 调用服务，开启吸泵。吸住物体
        pump_s=rospy.ServiceProxy("switch_pump_status",mirobotPump)
        # 打开气泵
        pump_s(True)
        joint_position=[0,0,0,0,0,0]
        arm.set_joint_value_target(joint_position) # 移动到归位点
        arm.go(wait=True)
        # 释放气泵
        pump_s(False)
    if plan_success: # 这里的判断条件需要根据你的实际逻辑来定
        rospy.loginfo("抓取动作成功。")
        return (True, "Grasp successful!")
    else:
        rospy.logwarn("抓取动作失败。")
        return (False, "MoveIt planning or execution failed.")

def handle_grasp_request(req):
    """
    这是ROS服务的回调函数。它接收请求，调用核心逻辑，并返回响应。
    """
    success, message = perform_grasp_logic(req.tag_id)
    # 创建并返回一个响应对象
    return GraspObjectResponse(success=success, message=message)

def grasp_service_server():
    global arm, listener, tf_broadcaster
    rospy.init_node('grasp_service_server')

    arm = moveit_commander.MoveGroupCommander('manipulator')
    arm.set_pose_reference_frame('base')
    listener = tf.TransformListener()
    tf_broadcaster = tf2_ros.transform_broadcaster.TransformBroadcaster()

    # 创建服务服务器
    s = rospy.Service('grasp_object', GraspObject, handle_grasp_request)
    print("Ready to grasp objects.")
    rospy.spin()

if __name__ == "__main__":
    grasp_service_server()