#!/usr/bin/env python
# coding=utf-8

import rospy
from sensor_msgs.msg import Image
import cv2
import cv_bridge
import numpy as np
from geometry_msgs.msg import Twist
from cv_bridge import CvBridge

linear_x = 0
angular_z = 0

class LineFollowerLogic:
    def __init__(self):
        # 1. 初始化变量，而不是ROS节点
        self.bridge = CvBridge()
        self.latest_twist_command = (0.0, 0.0) # (linear_x, angular_z)
        
        # 2. 订阅依然需要，用来接收数据
        # self.image_sub = rospy.Subscriber('/usb_cam/image_raw', Image, self.process_image)

        # --- PID 参数定义 ---
        self.Kp = 0.005
        self.Ki = 0.0
        self.Kd = 0.002
        self.PIDOutput = 0.0
        self.Error = 0.0
        self.LastError = 0.0
        self.LastLastError = 0.0

        # --- 新增：丢线寻线时的固定转向角速度 ---
        # 注意：这个值需要根据你的机器人和赛道宽度进行调整。
        # 正值通常对应左转。
        self.searching_turn_speed = 0 # 0.75
        # 寻线时的前进速度可以稍微降低
        self.searching_linear_speed = 0 # 0.12
        
        rospy.loginfo("巡线逻辑模块已初始化。")
    
    def find_nearest_large_blobs(self, mask, center_x, center_y, min_area):
        """
        在掩码中找到中心点左右两侧最近的、面积大于指定大小的色块。
        mask: 提取的掩码图像
        center_x: 中心点的 x 坐标
        center_y: 中心点的 y 坐标
        min_area: 最小色块面积
        return: 左侧和右侧最近的色块的边缘坐标
        """
        # 找到所有连通区域（色块）
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 初始化最近色块的变量
        nearest_left = None
        nearest_right = None
        min_dist_left = float('inf')
        min_dist_right = float('inf')

        for contour in contours:
            # 计算色块的面积
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            # 遍历轮廓点，找到最接近 center_y 的点
            left_points = []
            right_points = []
            for point in contour:
                x, y = point[0]
                dist_y = abs(y - center_y)
                # 判断点在左侧还是右侧
                if x < center_x:  # 左侧
                    left_points.append((x, y))
                else:  # 右侧
                    right_points.append((x, y))

            # 判断色块在左侧还是右侧占比更大
            if len(left_points) > len(right_points):
                # 色块在左侧占比更大，只处理左侧
                for x, y in left_points:
                    dist_y = abs(y - 174)
                    if dist_y < min_dist_left:
                        min_dist_left = dist_y
                        nearest_left = (x, y)
            else:
                # 色块在右侧占比更大，只处理右侧
                for x, y in right_points:
                    dist_y = abs(y - 174)
                    if dist_y < min_dist_right:
                        min_dist_right = dist_y
                        nearest_right = (x, y)

        return nearest_left, nearest_right
    def process_image(self, img):
        """
        检测车道线巡线移动
        """
        global linear_x, angular_z

        try:
            # 将 ROS 图像转换成 OpenCV 图像
            img = self.bridge.imgmsg_to_cv2(img, desired_encoding='bgr8')
        except cv_bridge.CvBridgeError as e:
            rospy.logerr("CvBridge Error: {0}".format(e))
            return
        # 将图像中 y 轴 0 到 200 的区域变成白色
        # img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
        # img[390:, :] = [255, 255, 255]  # 将指定区域设置为白色 (BGR 格式)
        imger = img.copy()
        imga = img.copy()
        
        cv2.imshow('imag', imga)
        cv2.rectangle(imga, (0, 330), (640, 480), (255, 255, 255), -1)
        cv2.rectangle(imga, (78, 300), (640, 345), (255, 255, 255), -1)
        cv2.rectangle(imga, (160, 230), (504, 312), (255, 255, 255), -1)
        cv2.imshow('rectangle imag', imga)

        hsv = cv2.cvtColor(imga, cv2.COLOR_BGR2HSV)
        lower_black = np.array([0, 0, 15])
        upper_black = np.array([130, 87, 100])
        mask = cv2.inRange(hsv, lower_black, upper_black)

        # 定义膨胀和腐蚀的核
        kerne1 = np.ones((8, 8), np.uint8)
        kerne2 = np.ones((4, 4), np.uint8)

        dilated_mask = cv2.dilate(mask, kerne1, iterations=1)  # 膨胀操作
        cv2.imshow('dilated imag', dilated_mask)
        mask = cv2.erode(dilated_mask, kerne2, iterations=1)  # 腐蚀操作
        cv2.imshow('erode imag', mask)

        # 定义中心点和最小面积
        center_x = 320
        center_y = 240
        min_area = 150

        # 找到左右两侧最近的色块
        nearest_left, nearest_right = self.find_nearest_large_blobs(mask, center_x, center_y, min_area)

        # 计算两个色块中心点之间的中点
        midpoint = None
        if nearest_left and nearest_right:
            midpoint_x = (nearest_left[0] + nearest_right[0]) // 2
            midpoint_y = (nearest_left[1] + nearest_right[1]) // 2
            # if self.xunxuan_send == 1:
            #     midpoint = (midpoint_x - 60, midpoint_y)
            # else:
            #     midpoint = (midpoint_x - 10, midpoint_y)
            midpoint = (midpoint_x - 10, midpoint_y)

        # 在原图上绘制结果
        if nearest_left:
            cv2.circle(imger, nearest_left, 5, (0, 255, 0), -1)  # 绿色圆圈标记左侧色块
        if nearest_right:
            cv2.circle(imger, nearest_right, 5, (0, 0, 255), -1)  # 红色圆圈标记右侧色块

        if midpoint and nearest_right and nearest_left and (nearest_right[0] - nearest_left[0] >= 320):
            # 左右都有线，且距离大于320
            cv2.circle(imger, midpoint, 5, (255, 0, 0), -1)  # 蓝色圆圈标记中点

            self.LastLastError = self.LastError
            self.LastError = self.Error
            self.Error = 640 / 2 - midpoint[0]

            # 计算增量
            IncrementalValue = (self.Kp * (self.Error - self.LastError) +
                                self.Ki * self.Error +
                                self.Kd * (self.Error - 2 * self.LastError + self.LastLastError))

            # 计算输出
            self.PIDOutput += IncrementalValue

            linear_x = 0.28
            angular_z = float(self.PIDOutput) / 4.5

        elif nearest_right:  # 只有右边有线
            linear_x = self.searching_linear_speed
            angular_z = self.searching_turn_speed
        elif nearest_left:  # 只有左边有线
            linear_x = self.searching_linear_speed
            angular_z = -self.searching_turn_speed

        xsy = angular_z
        rounded_num = round(xsy, 2)
        print(rounded_num)

        # self.cmd_vel_pub.publish(self.twist)
        self.latest_twist_command = (linear_x, angular_z)

        # 显示结果
        cv2.imshow('Original Image', imger)
        cv2.imshow('Mask', mask)
        cv2.waitKey(1)

    def get_control_command(self):
        """
        这是提供给大脑控制器的公共接口。
        它不进行计算，只返回最新计算出的结果。
        """
        return self.latest_twist_command