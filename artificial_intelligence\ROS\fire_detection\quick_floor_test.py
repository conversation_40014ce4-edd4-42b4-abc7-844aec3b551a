#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速楼层检测测试
用于验证楼层校准效果
"""

import cv2
import numpy as np
import os
import json
from ultralytics import YOLO

class QuickFloorTest:
    def __init__(self, model_path=r"D:\Python\artificial_intelligence\ROS\fire.pt"):
        """初始化快速楼层测试"""
        # 加载YOLOv8模型
        try:
            self.fire_model = YOLO(model_path)
            print(f"✓ YOLOv8模型加载成功")
        except Exception as e:
            print(f"✗ YOLOv8模型加载失败: {e}")
            self.fire_model = None
        
        self.confidence_threshold = 0.5
        self.calibration_data = None
    
    def load_calibration_data(self, calibration_file):
        """加载校准数据"""
        try:
            with open(calibration_file, 'r', encoding='utf-8') as f:
                self.calibration_data = json.load(f)
            print(f"✓ 校准数据加载成功: {calibration_file}")
            return True
        except Exception as e:
            print(f"✗ 校准数据加载失败: {e}")
            return False
    
    def detect_fire(self, image):
        """检测火焰"""
        if self.fire_model is None:
            return []
        
        try:
            results = self.fire_model(image, conf=self.confidence_threshold, verbose=False)
            
            fire_detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                        
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        confidence = float(box.conf[0].cpu().numpy())
                        
                        fire_detections.append({
                            'bbox': (x1, y1, x2, y2),
                            'center': (center_x, center_y),
                            'confidence': confidence
                        })
            
            return fire_detections
        except Exception as e:
            print(f"火焰检测失败: {e}")
            return []
    
    def determine_floor_with_calibration(self, fire_center_y):
        """使用校准数据确定楼层"""
        if self.calibration_data is None:
            return None
        
        floor_lines = self.calibration_data.get('floor_lines', [])
        if not floor_lines:
            return None
        
        floor_lines_sorted = sorted(floor_lines)
        
        # 找到火焰位置在哪两条线之间
        for i in range(len(floor_lines_sorted) - 1):
            if floor_lines_sorted[i] <= fire_center_y <= floor_lines_sorted[i + 1]:
                floor_number = len(floor_lines_sorted) - i - 1
                return max(1, floor_number)
        
        # 边界情况
        if fire_center_y < floor_lines_sorted[0]:
            return len(floor_lines_sorted)
        elif fire_center_y > floor_lines_sorted[-1]:
            return 1
        
        return None
    
    def determine_floor_default(self, fire_center_y, image_height, floors=5):
        """默认楼层检测算法"""
        building_start_y = int(image_height * 0.1)
        building_end_y = int(image_height * 0.9)
        building_height = building_end_y - building_start_y
        
        if building_start_y <= fire_center_y <= building_end_y:
            relative_y = fire_center_y - building_start_y
            floor_height = building_height / floors
            floor_number = floors - int(relative_y / floor_height)
            return max(1, min(floors, floor_number))
        
        if fire_center_y < building_start_y:
            return floors
        else:
            return 1
    
    def draw_comparison(self, image, fire_detections):
        """绘制对比结果"""
        result_img = image.copy()
        
        # 绘制校准数据的楼层线
        if self.calibration_data:
            floor_lines = self.calibration_data.get('floor_lines', [])
            for i, y in enumerate(floor_lines):
                cv2.line(result_img, (0, y), (result_img.shape[1], y), (0, 255, 0), 2)
                floor_num = len(floor_lines) - i
                cv2.putText(result_img, f"{floor_num}F", (10, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 绘制火焰检测结果
        for i, detection in enumerate(fire_detections):
            x1, y1, x2, y2 = detection['bbox']
            center_x, center_y = detection['center']
            confidence = detection['confidence']
            
            # 使用校准数据确定楼层
            calibrated_floor = self.determine_floor_with_calibration(center_y)
            
            # 使用默认算法确定楼层
            default_floor = self.determine_floor_default(center_y, image.shape[0])
            
            # 绘制边界框
            cv2.rectangle(result_img, (x1, y1), (x2, y2), (0, 0, 255), 3)
            
            # 绘制中心点
            cv2.circle(result_img, (center_x, center_y), 5, (255, 0, 0), -1)
            
            # 添加标签
            if calibrated_floor is not None:
                label = f"Fire-{i+1}: {calibrated_floor}F (校准)"
                color = (0, 255, 0)  # 绿色表示使用校准数据
            else:
                label = f"Fire-{i+1}: {default_floor}F (默认)"
                color = (0, 165, 255)  # 橙色表示使用默认算法
            
            confidence_label = f"Conf: {confidence:.2f}"
            
            # 绘制标签背景
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            cv2.rectangle(result_img, (x1, y1-50), (x1 + label_size[0] + 10, y1), color, -1)
            
            # 绘制文字
            cv2.putText(result_img, label, (x1 + 5, y1-30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(result_img, confidence_label, (x1 + 5, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 在控制台输出对比结果
            print(f"火焰 {i+1}:")
            print(f"  位置: ({center_x}, {center_y})")
            print(f"  置信度: {confidence:.3f}")
            if calibrated_floor is not None:
                print(f"  校准楼层: {calibrated_floor}楼")
            print(f"  默认楼层: {default_floor}楼")
            print()
        
        return result_img
    
    def test_image(self, image_path):
        """测试图像"""
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            return
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法加载图像: {image_path}")
            return
        
        print(f"测试图像: {os.path.basename(image_path)}")
        print(f"图像尺寸: {image.shape[1]}x{image.shape[0]}")
        
        # 尝试加载校准数据
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        calibration_file = f"{base_name}_floor_calibration.json"
        
        if os.path.exists(calibration_file):
            self.load_calibration_data(calibration_file)
        else:
            print("未找到校准数据文件")
            self.calibration_data = None
        
        # 检测火焰
        fire_detections = self.detect_fire(image)
        print(f"检测到 {len(fire_detections)} 处火焰")
        
        if not fire_detections:
            print("未检测到火焰")
            return
        
        # 绘制对比结果
        result_img = self.draw_comparison(image, fire_detections)
        
        # 显示结果
        cv2.namedWindow('Floor Detection Comparison', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Floor Detection Comparison', 1000, 700)
        cv2.imshow('Floor Detection Comparison', result_img)
        
        print("按任意键继续...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()

def main():
    """主函数"""
    print("快速楼层检测测试")
    print("=" * 40)
    
    tester = QuickFloorTest()
    
    if tester.fire_model is None:
        print("模型加载失败，程序退出")
        return
    
    while True:
        image_path = input("\n请输入图像路径 (或输入 'quit' 退出): ").strip()
        
        if image_path.lower() == 'quit':
            break
        
        if os.path.exists(image_path):
            tester.test_image(image_path)
        else:
            print(f"文件不存在: {image_path}")

if __name__ == '__main__':
    main()
