#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
火灾检测服务客户端
用于测试火灾检测服务
"""

import rospy
from std_srvs.srv import Trigger
import sys

class FireDetectionClient:
    def __init__(self):
        """初始化客户端"""
        rospy.init_node('fire_detection_client', anonymous=True)
        
        # 等待服务可用
        rospy.loginfo("等待火灾检测服务...")
        rospy.wait_for_service('fire_detection')
        
        # 创建服务代理
        self.fire_detection_service = rospy.ServiceProxy('fire_detection', Trigger)
        rospy.loginfo("火灾检测服务已连接")
    
    def call_fire_detection(self):
        """调用火灾检测服务"""
        try:
            rospy.loginfo("调用火灾检测服务...")
            response = self.fire_detection_service()
            
            if response.success:
                rospy.loginfo(f"检测成功: {response.message}")
                print(f"结果: {response.message}")
            else:
                rospy.logwarn(f"检测失败: {response.message}")
                print(f"失败: {response.message}")
                
            return response.success
            
        except rospy.ServiceException as e:
            rospy.logerr(f"服务调用失败: {e}")
            return False
    
    def run_continuous_detection(self, interval=5):
        """持续检测模式"""
        rospy.loginfo(f"开始持续检测，间隔{interval}秒...")
        rate = rospy.Rate(1.0/interval)  # 每interval秒检测一次
        
        while not rospy.is_shutdown():
            self.call_fire_detection()
            rate.sleep()

def main():
    """主函数"""
    try:
        client = FireDetectionClient()
        
        if len(sys.argv) > 1 and sys.argv[1] == 'continuous':
            # 持续检测模式
            interval = 5
            if len(sys.argv) > 2:
                try:
                    interval = int(sys.argv[2])
                except ValueError:
                    rospy.logwarn("无效的间隔时间，使用默认值5秒")
            
            client.run_continuous_detection(interval)
        else:
            # 单次检测模式
            client.call_fire_detection()
            
    except rospy.ROSInterruptException:
        rospy.loginfo("客户端已停止")
    except KeyboardInterrupt:
        rospy.loginfo("用户中断，客户端已停止")

if __name__ == '__main__':
    main()
