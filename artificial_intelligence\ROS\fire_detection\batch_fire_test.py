#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量火焰检测测试程序
可以批量处理多张图片，生成检测报告
"""

import cv2
import numpy as np
import os
import glob
import json
from datetime import datetime

class BatchFireTest:
    def __init__(self):
        """初始化批量火焰检测测试"""
        # 火焰检测参数
        self.fire_lower1 = np.array([0, 50, 50])      # 红色范围1
        self.fire_upper1 = np.array([10, 255, 255])
        self.fire_lower2 = np.array([170, 50, 50])    # 红色范围2  
        self.fire_upper2 = np.array([180, 255, 255])
        self.fire_lower3 = np.array([10, 50, 50])     # 橙色范围
        self.fire_upper3 = np.array([25, 255, 255])
        
        self.min_area = 200  # 最小火焰面积
        
        # 测试结果
        self.test_results = []
        
        print("批量火焰检测测试程序已初始化")
    
    def detect_fire(self, image):
        """检测火焰区域"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 创建火焰颜色掩码
        mask1 = cv2.inRange(hsv, self.fire_lower1, self.fire_upper1)
        mask2 = cv2.inRange(hsv, self.fire_lower2, self.fire_upper2)
        mask3 = cv2.inRange(hsv, self.fire_lower3, self.fire_upper3)
        
        fire_mask = cv2.bitwise_or(mask1, mask2)
        fire_mask = cv2.bitwise_or(fire_mask, mask3)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_CLOSE, kernel)
        fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(fire_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        fire_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                x, y, w, h = cv2.boundingRect(contour)
                center_x = x + w // 2
                center_y = y + h // 2
                
                fire_regions.append({
                    'bbox': (x, y, w, h),
                    'center': (center_x, center_y),
                    'area': area
                })
        
        return fire_regions, fire_mask
    
    def get_floor_number(self, center_y, image_height, total_floors=5):
        """根据Y坐标计算楼层"""
        floor_height = image_height / total_floors
        floor = total_floors - int(center_y / floor_height)
        return max(1, min(total_floors, floor))
    
    def process_single_image(self, image_path, save_results=True):
        """处理单张图像"""
        if not os.path.exists(image_path):
            return None
        
        image = cv2.imread(image_path)
        if image is None:
            return None
        
        # 检测火焰
        fire_regions, fire_mask = self.detect_fire(image)
        
        # 创建结果图像
        result_img = image.copy()
        
        # 绘制检测结果
        for i, region in enumerate(fire_regions):
            x, y, w, h = region['bbox']
            center = region['center']
            floor = self.get_floor_number(center[1], image.shape[0])
            
            # 绘制边界框
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 0, 255), 2)
            # 绘制中心点
            cv2.circle(result_img, center, 5, (255, 0, 0), -1)
            # 添加标签
            label = f"Fire-{i+1}: {floor}F"
            cv2.putText(result_img, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 保存结果
        if save_results:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            result_dir = "fire_test_results"
            os.makedirs(result_dir, exist_ok=True)
            
            result_path = os.path.join(result_dir, f"{base_name}_result.jpg")
            mask_path = os.path.join(result_dir, f"{base_name}_mask.jpg")
            
            cv2.imwrite(result_path, result_img)
            cv2.imwrite(mask_path, fire_mask)
        
        # 生成检测结果
        result = {
            'image_path': image_path,
            'image_size': image.shape,
            'fire_count': len(fire_regions),
            'fire_detected': len(fire_regions) > 0,
            'fire_regions': []
        }
        
        for i, region in enumerate(fire_regions):
            floor = self.get_floor_number(region['center'][1], image.shape[0])
            result['fire_regions'].append({
                'id': i + 1,
                'floor': floor,
                'center': region['center'],
                'area': region['area'],
                'bbox': region['bbox']
            })
        
        # 确定主要火灾位置
        if fire_regions:
            main_fire = max(fire_regions, key=lambda x: x['area'])
            main_floor = self.get_floor_number(main_fire['center'][1], image.shape[0])
            result['main_fire_floor'] = main_floor
            result['detection_result'] = f"检测到火灾 - {main_floor}楼"
        else:
            result['main_fire_floor'] = None
            result['detection_result'] = "未检测到火灾"
        
        return result
    
    def batch_process(self, input_path, file_pattern="*.jpg"):
        """批量处理图像"""
        self.test_results = []
        
        if os.path.isfile(input_path):
            # 单个文件
            image_files = [input_path]
        elif os.path.isdir(input_path):
            # 目录中的所有图像文件
            pattern = os.path.join(input_path, file_pattern)
            image_files = glob.glob(pattern)
            
            # 添加其他常见图像格式
            for ext in ['*.png', '*.jpeg', '*.bmp', '*.tiff']:
                if ext != file_pattern:
                    pattern = os.path.join(input_path, ext)
                    image_files.extend(glob.glob(pattern))
        else:
            print(f"错误: 路径不存在 - {input_path}")
            return
        
        if not image_files:
            print(f"未找到图像文件: {input_path}")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        print("开始批量处理...")
        
        # 处理每个图像文件
        for i, image_path in enumerate(image_files, 1):
            print(f"处理 {i}/{len(image_files)}: {os.path.basename(image_path)}")
            
            result = self.process_single_image(image_path)
            if result:
                self.test_results.append(result)
                print(f"  结果: {result['detection_result']}")
            else:
                print(f"  错误: 无法处理图像")
        
        print("批量处理完成!")
        self.generate_report()
    
    def generate_report(self):
        """生成检测报告"""
        if not self.test_results:
            print("没有测试结果可生成报告")
            return
        
        # 统计信息
        total_images = len(self.test_results)
        fire_detected_count = sum(1 for r in self.test_results if r['fire_detected'])
        total_fire_regions = sum(r['fire_count'] for r in self.test_results)
        
        # 楼层统计
        floor_stats = {}
        for result in self.test_results:
            if result['fire_detected']:
                floor = result['main_fire_floor']
                floor_stats[floor] = floor_stats.get(floor, 0) + 1
        
        # 生成报告
        report = {
            'test_summary': {
                'total_images': total_images,
                'fire_detected_images': fire_detected_count,
                'no_fire_images': total_images - fire_detected_count,
                'total_fire_regions': total_fire_regions,
                'detection_rate': f"{fire_detected_count/total_images*100:.1f}%"
            },
            'floor_statistics': floor_stats,
            'detailed_results': self.test_results,
            'test_time': datetime.now().isoformat()
        }
        
        # 保存JSON报告
        report_path = f"fire_detection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要报告
        print("\n" + "="*60)
        print("火焰检测测试报告")
        print("="*60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总图像数: {total_images}")
        print(f"检测到火焰: {fire_detected_count} 张 ({fire_detected_count/total_images*100:.1f}%)")
        print(f"未检测到火焰: {total_images - fire_detected_count} 张")
        print(f"总火焰区域数: {total_fire_regions}")
        
        if floor_stats:
            print("\n楼层分布:")
            for floor in sorted(floor_stats.keys()):
                print(f"  {floor}楼: {floor_stats[floor]} 次")
        
        print(f"\n详细报告已保存到: {report_path}")
        print("="*60)
        
        # 显示每个图像的结果
        print("\n详细结果:")
        for result in self.test_results:
            filename = os.path.basename(result['image_path'])
            print(f"  {filename}: {result['detection_result']}")
    
    def create_test_images(self, count=5):
        """创建测试图像集"""
        test_dir = "test_images"
        os.makedirs(test_dir, exist_ok=True)
        
        for i in range(count):
            # 创建基础楼宇图像
            img = np.ones((480, 640, 3), dtype=np.uint8) * 255
            
            # 绘制楼宇
            building_color = (139, 69, 19)
            cv2.rectangle(img, (200, 100), (440, 400), building_color, -1)
            
            # 绘制楼层线
            for j in range(1, 5):
                y = 100 + j * 60
                cv2.line(img, (200, y), (440, y), (0, 0, 0), 2)
            
            # 绘制窗户
            window_color = (173, 216, 230)
            for floor in range(5):
                for window in range(4):
                    x1 = 220 + window * 50
                    y1 = 110 + floor * 60
                    x2 = x1 + 30
                    y2 = y1 + 40
                    cv2.rectangle(img, (x1, y1), (x2, y2), window_color, -1)
            
            # 随机添加火焰
            if i < count // 2:  # 一半图像有火焰
                # 随机选择楼层
                fire_floor = np.random.randint(0, 5)
                fire_y = 130 + fire_floor * 60
                fire_x = 250 + np.random.randint(-30, 30)
                
                # 绘制火焰
                cv2.circle(img, (fire_x, fire_y), 15, (0, 69, 255), -1)
                cv2.circle(img, (fire_x+5, fire_y-5), 10, (0, 140, 255), -1)
            
            # 保存图像
            filename = f"test_building_{i+1:02d}.jpg"
            filepath = os.path.join(test_dir, filename)
            cv2.imwrite(filepath, img)
        
        print(f"已创建 {count} 张测试图像到 {test_dir} 目录")
        return test_dir

def main():
    """主函数"""
    tester = BatchFireTest()
    
    print("批量火焰检测测试程序")
    print("1. 输入图像文件路径或目录路径")
    print("2. 输入 'create' 创建测试图像集")
    print("3. 输入 'test' 创建并测试图像集")
    print("4. 输入 'quit' 退出程序")
    
    while True:
        choice = input("\n请输入选择: ").strip()
        
        if choice.lower() == 'quit':
            break
        elif choice.lower() == 'create':
            count = input("输入要创建的图像数量 (默认5): ").strip()
            count = int(count) if count.isdigit() else 5
            tester.create_test_images(count)
        elif choice.lower() == 'test':
            test_dir = tester.create_test_images()
            tester.batch_process(test_dir)
        else:
            if os.path.exists(choice):
                tester.batch_process(choice)
            else:
                print(f"路径不存在: {choice}")

if __name__ == '__main__':
    main()
