# 火灾检测ROS服务

这个项目实现了一个基于PaddleOCR的火灾检测ROS服务，能够识别楼宇名称、检测火灾位置并确定火灾发生的楼层。

## 功能特性

- 使用PaddleOCR进行中文楼宇名称识别
- 基于颜色检测的火灾识别
- 自动确定火灾发生的楼层
- 格式化输出：楼宇名称 + 几楼发现火灾

## 依赖安装

### 1. 安装PaddleOCR
```bash
pip install paddlepaddle
pip install paddleocr
```

### 2. 安装其他Python依赖
```bash
pip install opencv-python
pip install numpy
```

### 3. ROS依赖
确保已安装以下ROS包：
- cv_bridge
- sensor_msgs
- std_srvs

## 文件说明

- `fire_detection_service.py`: 主服务文件，实现火灾检测功能
- `fire_detection_client.py`: 客户端测试文件
- `fire_detection.launch`: ROS启动文件
- `README_fire_detection.md`: 说明文档

## 使用方法

### 1. 启动ROS核心
```bash
roscore
```

### 2. 启动摄像头节点（如果使用USB摄像头）
```bash
rosrun usb_cam usb_cam_node
```

### 3. 启动火灾检测服务
```bash
# 方法1: 直接运行Python文件
cd artificial_intelligence/ROS
python fire_detection_service.py

# 方法2: 使用rosrun（需要将文件放在ROS包中）
rosrun your_package_name fire_detection_service.py

# 方法3: 使用launch文件
roslaunch your_package_name fire_detection.launch
```

### 4. 测试服务

#### 单次检测
```bash
python fire_detection_client.py
```

#### 持续检测（每5秒检测一次）
```bash
python fire_detection_client.py continuous
```

#### 自定义检测间隔（每10秒检测一次）
```bash
python fire_detection_client.py continuous 10
```

#### 使用ROS服务命令行工具
```bash
rosservice call /fire_detection "{}"
```

## 服务接口

### 服务名称
`/fire_detection`

### 服务类型
`std_srvs/Trigger`

### 输入
无需输入参数

### 输出
- `success` (bool): 服务调用是否成功
- `message` (string): 检测结果，格式为"楼宇名称 几楼发现火灾"或"楼宇名称 未发现火灾"

## 配置参数

### 火灾检测参数
在`fire_detection_service.py`中可以调整以下参数：

```python
# 火焰颜色范围（HSV）
self.fire_color_lower = np.array([0, 50, 50])    # 下限
self.fire_color_upper = np.array([10, 255, 255]) # 上限
self.fire_color_lower2 = np.array([170, 50, 50]) # 下限2
self.fire_color_upper2 = np.array([180, 255, 255]) # 上限2

# 最小火焰面积阈值
area_threshold = 500  # 像素
```

### 楼层检测参数
```python
# 默认楼层数
floors = 5
```

## 工作原理

1. **图像获取**: 从`/camera/image_raw`话题订阅图像数据
2. **楼宇名称识别**: 使用PaddleOCR识别图像中的中文文字，筛选包含楼宇关键词的文本
3. **火灾检测**: 基于HSV颜色空间检测红色/橙色火焰区域
4. **楼层确定**: 根据火焰在图像中的垂直位置计算所在楼层
5. **结果输出**: 格式化输出检测结果

## 注意事项

1. 确保摄像头正常工作并发布图像到正确的话题
2. 光照条件会影响火焰检测效果，可能需要调整颜色阈值
3. 楼层检测基于图像中的垂直位置，实际使用时可能需要根据具体场景调整算法
4. PaddleOCR首次运行时会下载模型文件，需要网络连接

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查PaddleOCR是否正确安装
   - 确认ROS环境变量设置正确

2. **无法接收图像**
   - 检查摄像头是否正常工作
   - 确认图像话题名称是否正确

3. **OCR识别效果差**
   - 检查图像质量和光照条件
   - 调整摄像头位置和角度

4. **火灾检测误报**
   - 调整火焰颜色检测阈值
   - 增加最小面积阈值

## 扩展功能

可以考虑添加以下功能：
- 多楼宇同时检测
- 火灾严重程度评估
- 历史检测记录保存
- 报警通知功能
- 更精确的楼层检测算法
