#!/usr/bin/env python
# coding=utf-8

import rospy
from sensor_msgs.msg import Image
import cv2
import cv_bridge
import numpy as np
from geometry_msgs.msg import Twist

class Follower:
    def __init__(self):
        """
        初始化 ROS 节点、订阅者、发布者以及 PID 控制器参数。
        """
        self.bridge = cv_bridge.CvBridge()
        self.image_sub = rospy.Subscriber("/usb_cam/image_raw", Image, self.image_callback)
        self.cmd_vel_pub = rospy.Publisher("/cmd_vel", Twist, queue_size=1)
        self.twist = Twist()

        # --- PID 参数定义 ---
        self.Kp = 0.005
        self.Ki = 0.0
        self.Kd = 0.002
        self.PIDOutput = 0.0
        self.Error = 0.0
        self.LastError = 0.0
        self.LastLastError = 0.0

        # --- 新增：丢线寻线时的固定转向角速度 ---
        # 注意：这个值需要根据你的机器人和赛道宽度进行调整。
        # 正值通常对应左转。
        self.searching_turn_speed = 0.25
        # 寻线时的前进速度可以稍微降低
        self.searching_linear_speed = 0.7


        # --- 新增: ROI (Region of Interest) 参数 ---
        # 通过调整这两个值，来确定要处理的图像区域
        # 从图像顶部切除的像素行数，用于忽略远处的干扰
        self.roi_top_cut = 10  # 示例值 (图像高度的一半)，需要根据实际情况调试
        # 从图像底部切除的像素行数，用于忽略车头
        self.roi_bottom_cut = 200 # 示例值，需要根据实际情况调试

    def mid(self, follow, mask):
        """
        通过从下至上扫描图像的每一行，来拟合双边赛道的中线。
        (此函数保持不变)
        """
        h, w = follow.shape[:2]
        halfWidth = w // 2
        half = halfWidth
        # mid_output_y = int(h * 3 / 4)
        mid_output_y = 400
        mid_output_x = w // 2

        for y in range(h - 1, -1, -1):
            left_roi = mask[y, 0:half]
            if np.all(left_roi == 0):
                left = 0
            else:
                left = np.average(np.where(left_roi == 255))

            right_roi = mask[y, half:w]
            if np.all(right_roi == 0):
                right = w
            else:
                right = np.average(np.where(right_roi == 255)) + half

            mid_point = (left + right) / 2
            half = int(mid_point)
            
            follow[y, max(0, min(w - 1, int(mid_point)))] = 127

            if y == mid_output_y:
                mid_output_x = int(mid_point)

        error = float(mid_output_x - w // 2)
        cv2.circle(follow, (mid_output_x, mid_output_y), 8, 255, -1)
        return follow, error
    
    # --- 新增函数：检测视野中赛道线的状态 ---
    def detect_line_status(self, mask):
        """
        分析二值化图像，判断赛道线的可见状态。

        Args:
            mask (np.array): 包含赛道线的二值化图像。

        Returns:
            str: "BOTH", "LEFT_ONLY", "RIGHT_ONLY", 或 "NONE".
        """
        h, w = mask.shape
        # 我们更关心图像下半部分，因为它离机器人更近
        # roi = mask[h // 2:, :]
        
        # 将ROI分割为左右两部分
        left_half = mask[:, :w // 2]
        right_half = mask[:, w // 2:]

        # 检查左右两半是否含有白色像素（赛道线）
        is_left_present = np.any(left_half == 255)
        is_right_present = np.any(right_half == 255)

        if is_left_present and is_right_present:
            return "BOTH"
        elif is_left_present and not is_right_present:
            return "LEFT_ONLY"
        elif not is_left_present and is_right_present:
            return "RIGHT_ONLY"
        else:
            return "NONE"

    def image_callback(self, msg):
        """
        主回调函数，根据赛道线状态执行不同控制策略。
        """
        # 将ROS图像消息转换为OpenCV图像（BGR格式）
        image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        # image = cv2.resize(image, (640, 480))
        h, w, _ = image.shape # 获取图像尺寸,opencv是先高后宽
        print("图像高：{}宽：{}".format(h, w))

        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        lower_black = np.array([0, 77, 0])
        upper_black = np.array([180, 255, 87])
        mask = cv2.inRange(hsv, lower_black, upper_black)

        # --- 新增: 定义并应用ROI ---
        # 创建一个全黑的掩码，尺寸与原图相同
        roi_mask = np.zeros((h, w), dtype=np.uint8)
        # 定义ROI的有效y坐标范围
        roi_start_y = self.roi_top_cut
        roi_end_y = h - self.roi_bottom_cut
        # 在全黑掩码上，将ROI区域填充为白色。-1表示填充整个矩形。
        cv2.rectangle(roi_mask, (0, roi_start_y), (w, roi_end_y), 255, -1)
        # 将ROI掩码应用到经过形态学处理的赛道掩码上
        cut_mask = cv2.bitwise_and(mask, roi_mask)

        # --- 新增：两种形态学闭运算实现方式 (二选一) ---
        # 共同定义一个内核（Kernel）。大小是需要调试的关键参数。
        # 可以尝试 (3, 3), (5, 5), (7, 7) 等尺寸。
        kernel = np.ones((5, 5), np.uint8) 
        
        # --- 方法一 (推荐): 使用OpenCV内置的形态学闭运算函数 ---
        # 这种方法代码更简洁，通常效率也更高。
        #processed_mask = cv2.morphologyEx(cut_mask, cv2.MORPH_CLOSE, kernel)

        # --- 方法二 (手动实现): 先膨胀，后腐蚀 ---
        # 解除以下两行注释，并注释掉上面的“方法一”那一行，即可切换到此方法。
        # 这种方法有助于理解闭运算的原理。
        # temp_mask = cv2.dilate(mask, kernel, iterations=1)
        # processed_mask = cv2.erode(temp_mask, kernel, iterations=1)
        processed_mask = cv2.dilate(cut_mask, kernel, iterations=1)

        # --- 3. 新增: ROI可视化 ---
        # 复制原始图像用于绘制，以免影响原始图像
        visualization_img = image.copy()
        # 定义颜色 (BGR格式) 和透明度
        COLOR_RED = (0, 0, 255); COLOR_GREEN = (0, 255, 0)
        ALPHA = 0.3
        # 创建一个覆盖层
        overlay = visualization_img.copy()
        # 绘制保留区域（绿色框）
        cv2.rectangle(visualization_img, (0, roi_start_y), (w, roi_end_y), COLOR_GREEN, 2)
        # 绘制顶部剪切区域（红色半透明）
        cv2.rectangle(overlay, (0, 0), (w, roi_start_y), COLOR_RED, -1)
        # 绘制底部剪切区域（红色半透明）
        cv2.rectangle(overlay, (0, roi_end_y), (w, h), COLOR_RED, -1)
        # 将覆盖层与原图混合
        cv2.addWeighted(overlay, ALPHA, visualization_img, 1 - ALPHA, 0, visualization_img)

        # --- 核心逻辑修改：基于赛道线状态进行决策 ---
        line_status = self.detect_line_status(processed_mask)
        # processed_img = mask.copy() # 默认显示原始mask
        display_img = processed_mask.copy()

        if line_status == "BOTH":
            # 状态1: 两条线都在，正常执行PID巡线
            print("Status: BOTH lines detected. Following midline.")
            try:
                display_img, new_error = self.mid(display_img, processed_mask)

                # PID 控制计算
                self.LastLastError = self.LastError
                self.LastError = self.Error
                self.Error = new_error
                IncrementalValue = (self.Kp * (self.Error - self.LastError) +
                                  self.Ki * self.Error +
                                  self.Kd * (self.Error - 2 * self.LastError + self.LastLastError))
                self.PIDOutput += IncrementalValue
                self.PIDOutput = max(min(self.PIDOutput, 2.5), -2.5)

                # 发布控制指令
                self.twist.linear.x = 0.7 # 正常巡线速度
                self.twist.angular.z = -self.PIDOutput
            
            except (ValueError, ZeroDivisionError):
                # 即使检测到两条线，mid()也可能因断点等原因计算失败，此时停车保证安全
                print("Warning: Midpoint calculation failed unexpectedly. Stopping.")
                self.twist.linear.x = 0.2
                self.twist.angular.z = 0.0

        elif line_status == "RIGHT_ONLY":
            # 状态2: 只看到右侧线，向左转寻找左侧线
            print("Status: RIGHT line only. Searching LEFT.")
            self.twist.linear.x = self.searching_linear_speed
            # 赋予一个固定的角速度向左转
            # 在ROS中，角速度为正通常是逆时针转，即向左
            self.twist.angular.z = self.searching_turn_speed

        elif line_status == "LEFT_ONLY":
            # 状态3: 只看到左侧线，向右转寻找右侧线
            print("Status: LEFT line only. Searching RIGHT.")
            self.twist.linear.x = self.searching_linear_speed
            # 赋予一个固定的角速度向右转
            self.twist.angular.z = -self.searching_turn_speed
        
        else: # line_status == "NONE"
            # 状态4: 一条线都看不到，原地停车
            print("Status: NO lines detected. Stopping.")
            self.twist.linear.x = 0.0
            self.twist.angular.z = 0.0
        
        # 统一发布指令和显示图像
        self.cmd_vel_pub.publish(self.twist)
        
        cv2.imshow("Original Image", image)
        cv2.imshow("Mask image", mask) # hsv提取后图像
        cv2.imshow("cut mask image", cut_mask) # roi处理后图像
        cv2.imshow("shape image", processed_mask) # 对roi处理后图像，进行形态学操作后图像
        cv2.imshow("ROI Visualization", visualization_img) # 显示带ROI框的图像
        cv2.imshow("Final Processed Mask", display_img)   # 显示描绘中线的图像
        cv2.waitKey(3)


if __name__ == '__main__':
    rospy.init_node("dual_line_follower_search") # 使用新的节点名
    follower = Follower()
    rospy.spin()
    cv2.destroyAllWindows()