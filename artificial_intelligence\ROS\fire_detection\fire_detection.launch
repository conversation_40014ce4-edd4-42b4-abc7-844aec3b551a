<?xml version="1.0"?>
<launch>
    <!-- 火灾检测服务启动文件 -->
    
    <!-- 启动火灾检测服务 -->
    <node name="fire_detection_service" pkg="your_package_name" type="fire_detection_service.py" output="screen">
        <remap from="/camera/image_raw" to="/camera/image_raw"/>
    </node>
    
    <!-- 可选：启动摄像头节点（如果需要） -->
    <!-- 
    <node name="usb_cam" pkg="usb_cam" type="usb_cam_node" output="screen">
        <param name="video_device" value="/dev/video0"/>
        <param name="image_width" value="640"/>
        <param name="image_height" value="480"/>
        <param name="pixel_format" value="yuyv"/>
        <param name="camera_frame_id" value="usb_cam"/>
        <param name="io_method" value="mmap"/>
    </node>
    -->
    
</launch>
