#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
火灾检测服务
使用PaddleOCR识别楼宇名称，检测火灾位置，并返回格式化结果
"""

import rospy
import cv2
import numpy as np
from std_srvs.srv import Trigger, TriggerResponse
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import os
import re
from paddleocr import PaddleOCR

class FireDetectionService:
    def __init__(self):
        """初始化火灾检测服务"""
        rospy.init_node('fire_detection_service', anonymous=True)
        
        # 初始化PaddleOCR
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        
        # 初始化CV Bridge
        self.bridge = CvBridge()
        
        # 当前图像
        self.current_image = None
        
        # 订阅图像话题
        self.image_sub = rospy.Subscriber('/camera/image_raw', Image, self.image_callback)
        
        # 创建服务
        self.service = rospy.Service('fire_detection', Trigger, self.fire_detection_callback)
        
        # 火灾检测参数
        self.fire_color_lower = np.array([0, 50, 50])    # 火焰颜色下限(HSV)
        self.fire_color_upper = np.array([10, 255, 255]) # 火焰颜色上限(HSV)
        self.fire_color_lower2 = np.array([170, 50, 50]) # 火焰颜色下限2(HSV)
        self.fire_color_upper2 = np.array([180, 255, 255]) # 火焰颜色上限2(HSV)
        
        rospy.loginfo("火灾检测服务已启动")
    
    def image_callback(self, msg):
        """图像回调函数"""
        try:
            self.current_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
        except Exception as e:
            rospy.logerr(f"图像转换失败: {e}")
    
    def detect_building_name(self, image):
        """使用PaddleOCR检测楼宇名称"""
        try:
            # 使用OCR识别文字
            result = self.ocr.ocr(image, cls=True)
            
            building_name = ""
            max_confidence = 0
            
            if result and result[0]:
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    
                    # 查找包含楼宇相关关键词的文本
                    if any(keyword in text for keyword in ['楼', '大厦', '中心', '广场', '大楼', '建筑']):
                        if confidence > max_confidence:
                            max_confidence = confidence
                            building_name = text
                    
                    # 如果没有找到楼宇关键词，选择置信度最高的文本作为楼宇名称
                    if not building_name and confidence > max_confidence:
                        max_confidence = confidence
                        building_name = text
            
            return building_name if building_name else "未知楼宇"
            
        except Exception as e:
            rospy.logerr(f"OCR识别失败: {e}")
            return "未知楼宇"
    
    def detect_fire_location(self, image):
        """检测火灾位置"""
        try:
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 创建火焰颜色掩码
            mask1 = cv2.inRange(hsv, self.fire_color_lower, self.fire_color_upper)
            mask2 = cv2.inRange(hsv, self.fire_color_lower2, self.fire_color_upper2)
            fire_mask = cv2.bitwise_or(mask1, mask2)
            
            # 形态学操作去除噪声
            kernel = np.ones((5, 5), np.uint8)
            fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_CLOSE, kernel)
            fire_mask = cv2.morphologyEx(fire_mask, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(fire_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None, None
            
            # 找到最大的火焰区域
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            
            # 如果面积太小，认为不是火灾
            if area < 500:  # 可调整的阈值
                return None, None
            
            # 计算火焰中心点
            M = cv2.moments(largest_contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy), area
            
            return None, None
            
        except Exception as e:
            rospy.logerr(f"火灾检测失败: {e}")
            return None, None
    
    def determine_floor_from_position(self, fire_position, image_height):
        """根据火焰位置确定楼层"""
        if fire_position is None:
            return None
        
        cx, cy = fire_position
        
        # 假设图像中楼层是垂直分布的
        # 将图像高度分为若干层（这里假设5层楼）
        floors = 5
        floor_height = image_height / floors
        
        # 计算火焰所在楼层（从下往上数）
        floor_number = floors - int(cy / floor_height)
        floor_number = max(1, min(floors, floor_number))  # 确保在1-5层范围内
        
        return floor_number
    
    def analyze_building_structure(self, image):
        """分析楼宇结构，更准确地确定楼层"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 检测水平线（楼层分界线）
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            horizontal_lines = []
            if lines is not None:
                for rho, theta in lines[:, 0]:
                    # 筛选水平线（角度接近0或π）
                    if abs(theta) < 0.2 or abs(theta - np.pi) < 0.2:
                        horizontal_lines.append(rho)
            
            # 如果检测到水平线，使用它们来确定楼层
            if len(horizontal_lines) > 1:
                horizontal_lines.sort()
                return len(horizontal_lines) - 1  # 楼层数 = 水平线数 - 1
            
            return 5  # 默认5层
            
        except Exception as e:
            rospy.logerr(f"楼宇结构分析失败: {e}")
            return 5
    
    def fire_detection_callback(self, req):
        """火灾检测服务回调函数"""
        response = TriggerResponse()
        
        if self.current_image is None:
            response.success = False
            response.message = "未接收到图像数据"
            return response
        
        try:
            # 1. 识别楼宇名称
            building_name = self.detect_building_name(self.current_image)
            rospy.loginfo(f"识别到楼宇名称: {building_name}")
            
            # 2. 检测火灾位置
            fire_position, fire_area = self.detect_fire_location(self.current_image)
            
            if fire_position is None:
                response.success = True
                response.message = f"{building_name} 未发现火灾"
                return response
            
            # 3. 确定楼层
            image_height = self.current_image.shape[0]
            floor_number = self.determine_floor_from_position(fire_position, image_height)
            
            # 4. 格式化输出
            result_message = f"{building_name} {floor_number}楼发现火灾"
            
            response.success = True
            response.message = result_message
            
            rospy.loginfo(f"火灾检测结果: {result_message}")
            rospy.loginfo(f"火焰位置: {fire_position}, 面积: {fire_area}")
            
            return response
            
        except Exception as e:
            rospy.logerr(f"火灾检测服务执行失败: {e}")
            response.success = False
            response.message = f"检测失败: {str(e)}"
            return response
    
    def run(self):
        """运行服务"""
        rospy.loginfo("火灾检测服务正在运行...")
        rospy.spin()

if __name__ == '__main__':
    try:
        service = FireDetectionService()
        service.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("火灾检测服务已停止")
