#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
楼层校准工具
帮助用户手动标定楼层位置，提高楼层检测准确性
"""

import cv2
import numpy as np
import os
import json

class FloorCalibrationTool:
    def __init__(self):
        """初始化楼层校准工具"""
        self.image = None
        self.floor_lines = []
        self.current_floor_count = 5
        self.calibration_data = {}
        
        print("楼层校准工具已初始化")
        print("使用说明:")
        print("- 左键点击: 标记楼层分界线")
        print("- 右键点击: 删除最近的标记线")
        print("- 按 'r': 重置所有标记")
        print("- 按 's': 保存校准数据")
        print("- 按 'q': 退出")
    
    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 左键点击，添加楼层线
            self.floor_lines.append(y)
            self.floor_lines.sort()
            print(f"添加楼层线: y={y}, 当前共{len(self.floor_lines)}条线")
            self.draw_calibration()
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右键点击，删除最近的楼层线
            if self.floor_lines:
                removed_y = self.floor_lines.pop()
                print(f"删除楼层线: y={removed_y}")
                self.draw_calibration()
    
    def draw_calibration(self):
        """绘制校准界面"""
        if self.image is None:
            return
        
        # 创建显示图像
        display_img = self.image.copy()
        
        # 绘制楼层分界线
        for i, y in enumerate(self.floor_lines):
            cv2.line(display_img, (0, y), (display_img.shape[1], y), (0, 255, 0), 2)
            # 添加楼层标签
            floor_num = len(self.floor_lines) - i
            cv2.putText(display_img, f"{floor_num}F", (10, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 如果有楼层线，标记楼层区域
        if len(self.floor_lines) >= 2:
            for i in range(len(self.floor_lines) - 1):
                y1 = self.floor_lines[i]
                y2 = self.floor_lines[i + 1]
                floor_num = len(self.floor_lines) - i - 1
                
                # 绘制楼层区域背景
                overlay = display_img.copy()
                cv2.rectangle(overlay, (0, y1), (display_img.shape[1], y2), 
                             (0, 255, 0), -1)
                cv2.addWeighted(display_img, 0.9, overlay, 0.1, 0, display_img)
                
                # 在楼层中央添加楼层号
                center_y = (y1 + y2) // 2
                cv2.putText(display_img, f"第{floor_num}层", 
                           (display_img.shape[1]//2, center_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
        
        # 添加说明文字
        instructions = [
            "左键: 添加楼层线",
            "右键: 删除最后一条线", 
            "R: 重置",
            "S: 保存",
            "Q: 退出"
        ]
        
        for i, instruction in enumerate(instructions):
            cv2.putText(display_img, instruction, (10, 30 + i*25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        cv2.imshow('Floor Calibration', display_img)
    
    def auto_detect_floors(self):
        """自动检测楼层线（作为起始参考）"""
        if self.image is None:
            return
        
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
            
            # 检测水平边缘
            edges = cv2.Canny(gray, 50, 150)
            
            # 使用霍夫变换检测直线
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=int(self.image.shape[1]*0.3))
            
            horizontal_lines = []
            if lines is not None:
                for rho, theta in lines[:, 0]:
                    # 筛选水平线
                    if abs(theta) < 0.2 or abs(theta - np.pi) < 0.2:
                        y_line = int(rho / np.sin(theta)) if abs(np.sin(theta)) > 0.1 else None
                        if y_line is not None and 0 < y_line < self.image.shape[0]:
                            horizontal_lines.append(y_line)
            
            # 去重并排序
            horizontal_lines = sorted(list(set(horizontal_lines)))
            
            # 过滤太近的线
            filtered_lines = []
            min_distance = self.image.shape[0] // 10  # 最小距离
            
            for line in horizontal_lines:
                if not filtered_lines or abs(line - filtered_lines[-1]) > min_distance:
                    filtered_lines.append(line)
            
            self.floor_lines = filtered_lines
            print(f"自动检测到 {len(self.floor_lines)} 条楼层线")
            
        except Exception as e:
            print(f"自动检测失败: {e}")
    
    def calibrate_image(self, image_path):
        """校准图像"""
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            return None
        
        # 加载图像
        self.image = cv2.imread(image_path)
        if self.image is None:
            print(f"无法加载图像: {image_path}")
            return None
        
        print(f"开始校准图像: {os.path.basename(image_path)}")
        print(f"图像尺寸: {self.image.shape[1]}x{self.image.shape[0]}")
        
        # 重置楼层线
        self.floor_lines = []
        
        # 创建窗口
        cv2.namedWindow('Floor Calibration', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Floor Calibration', 1000, 700)
        cv2.setMouseCallback('Floor Calibration', self.mouse_callback)
        
        # 尝试自动检测
        print("尝试自动检测楼层线...")
        self.auto_detect_floors()
        
        # 初始绘制
        self.draw_calibration()
        
        # 主循环
        while True:
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('r'):
                # 重置
                self.floor_lines = []
                print("重置所有楼层线")
                self.draw_calibration()
            elif key == ord('s'):
                # 保存校准数据
                self.save_calibration_data(image_path)
            elif key == ord('a'):
                # 重新自动检测
                print("重新自动检测楼层线...")
                self.auto_detect_floors()
                self.draw_calibration()
        
        cv2.destroyAllWindows()
        return self.get_calibration_result()
    
    def save_calibration_data(self, image_path):
        """保存校准数据"""
        if not self.floor_lines:
            print("没有楼层线可保存")
            return
        
        # 准备校准数据
        calibration_data = {
            'image_path': image_path,
            'image_size': [self.image.shape[1], self.image.shape[0]],
            'floor_lines': self.floor_lines,
            'floor_count': len(self.floor_lines) + 1 if len(self.floor_lines) > 0 else self.current_floor_count
        }
        
        # 保存到JSON文件
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        calibration_file = f"{base_name}_floor_calibration.json"
        
        with open(calibration_file, 'w', encoding='utf-8') as f:
            json.dump(calibration_data, f, ensure_ascii=False, indent=2)
        
        print(f"校准数据已保存到: {calibration_file}")
        
        # 同时保存标记图像
        marked_image = self.image.copy()
        for i, y in enumerate(self.floor_lines):
            cv2.line(marked_image, (0, y), (marked_image.shape[1], y), (0, 255, 0), 3)
            floor_num = len(self.floor_lines) - i
            cv2.putText(marked_image, f"{floor_num}F", (10, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        marked_image_file = f"{base_name}_floor_marked.jpg"
        cv2.imwrite(marked_image_file, marked_image)
        print(f"标记图像已保存到: {marked_image_file}")
    
    def get_calibration_result(self):
        """获取校准结果"""
        if not self.floor_lines:
            return None
        
        return {
            'floor_lines': self.floor_lines,
            'floor_count': len(self.floor_lines) + 1 if len(self.floor_lines) > 0 else self.current_floor_count,
            'building_top': min(self.floor_lines) if self.floor_lines else 0,
            'building_bottom': max(self.floor_lines) if self.floor_lines else self.image.shape[0]
        }
    
    def load_calibration_data(self, calibration_file):
        """加载校准数据"""
        try:
            with open(calibration_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.floor_lines = data.get('floor_lines', [])
            self.current_floor_count = data.get('floor_count', 5)
            
            print(f"已加载校准数据: {len(self.floor_lines)}条楼层线")
            return data
            
        except Exception as e:
            print(f"加载校准数据失败: {e}")
            return None

def main():
    """主函数"""
    calibrator = FloorCalibrationTool()
    
    print("楼层校准工具")
    print("=" * 40)
    
    while True:
        image_path = input("\n请输入图像路径 (或输入 'quit' 退出): ").strip()
        
        if image_path.lower() == 'quit':
            break
        
        if os.path.exists(image_path):
            result = calibrator.calibrate_image(image_path)
            if result:
                print("\n校准完成!")
                print(f"楼层线位置: {result['floor_lines']}")
                print(f"楼层数: {result['floor_count']}")
        else:
            print(f"文件不存在: {image_path}")

if __name__ == '__main__':
    main()
