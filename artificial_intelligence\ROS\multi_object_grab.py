# -*- coding: UTF-8 -*-
"""
多物块连续抓取脚本
基于AprilTag视觉识别，实现连续抓取三个物块的功能
"""

import transforms3d as tfs
import time
import tf
from geometry_msgs.msg import TransformStamped
import tf2_ros.transform_broadcaster
import math   
import rospy, sys
import moveit_commander
from moveit_msgs.msg import RobotTrajectory
from trajectory_msgs.msg import JointTrajectoryPoint
from sensor_msgs.msg import Image
import numpy as np
from geometry_msgs.msg import PoseStamped, Pose
from std_msgs.msg import String
from tf.transformations import euler_from_quaternion, quaternion_from_euler
from sensor_msgs.msg import Image
from cv_bridge import CvBridge, CvBridgeError
import cv2
from mirobot_urdf_2.srv import *

# 物块抓取参数配置
# 每个物块的偏移量配置 (x, y, z)
GRAB_OFFSETS = {
    1: {"above": (-0.07, 0.004, 0.003), "grab": (0.025, 0, 0)},  # 物块1
    2: {"above": (-0.06, 0.002, 0.005), "grab": (0.020, 0, 0)},  # 物块2
    3: {"above": (-0.08, 0.006, 0.002), "grab": (0.030, 0, 0)}   # 物块3
}

def initialize_system():
    """
    初始化系统组件
    """
    # 初始化ROS节点
    rospy.init_node("multi_object_grab")
    rate = rospy.Rate(10.0)

    # 初始化MoveIt!系统
    moveit_commander.roscpp_initialize(sys.argv)
    # 创建机械臂控制对象
    arm = moveit_commander.MoveGroupCommander('manipulator')
    # 设置机械臂的参考坐标系
    arm.set_pose_reference_frame('base')
    # 创建TF监听器
    listener = tf.TransformListener()
    
    return arm, listener

def list_available_frames(listener):
    """
    列出当前所有可用的TF帧，用于调试
    """
    try:
        # 等待一小段时间以确保TF树更新
        rospy.sleep(0.1)
        # 获取所有可用的帧
        frames = listener.getFrameStrings()
        print("当前可用的TF帧:")
        for frame in frames:
            print("  - {}".format(frame))
        return frames
    except Exception as e:
        print("无法获取TF帧列表: {}".format(str(e)))
        return []

def detect_camera_frames(listener):
    """
    检测可用的相机帧
    """
    try:
        frames = list_available_frames(listener)
        camera_frames = [f for f in frames if 'camera' in f]
        optical_frames = [f for f in frames if 'optical' in f]
        
        print("检测到的相机相关帧:")
        if camera_frames:
            for frame in camera_frames:
                print("  - {}".format(frame))
        else:
            print("  没有找到包含'camera'的帧")
            
        print("检测到的光学帧:")
        if optical_frames:
            for frame in optical_frames:
                print("  - {}".format(frame))
        else:
            print("  没有找到包含'optical'的帧")
            
        return camera_frames, optical_frames
    except Exception as e:
        print("检测相机帧时出错: {}".format(str(e)))
        return [], []

def detect_apriltag(listener, tag_id):
    """
    检测指定ID的AprilTag并获取其姿态
    
    Args:
        listener: TF监听器
        tag_id: AprilTag ID
    
    Returns:
        posestamped: AprilTag的姿态信息
    """
    get_pose = False
    posestamped = PoseStamped()
    
    # 尝试次数计数器
    attempt_count = 0
    max_attempts = 10
    
    while not rospy.is_shutdown() and not get_pose and attempt_count < max_attempts:
        try:
            attempt_count += 1
            print("尝试检测Tag {} (第{}次尝试)".format(tag_id, attempt_count))
            
            # 首先检查可用的帧
            available_frames = list_available_frames(listener)
            
            # 尝试不同的相机帧
            camera_frames_to_try = [
                'camera_color_optical_frame',
                'camera_depth_optical_frame',
                'camera_link'
            ]
            
            tag_frame = "tag_{}".format(tag_id)
            found_transform = False
            camera_frame_used = None
            
            # 遍历尝试不同的相机帧
            for camera_frame in camera_frames_to_try:
                if camera_frame in available_frames:
                    print("尝试使用相机帧: {}".format(camera_frame))
                    try:
                        now = rospy.Time.now()
                        listener.waitForTransform(camera_frame, tag_frame, now, rospy.Duration(1.0))
                        (trans, rot) = listener.lookupTransform(camera_frame, tag_frame, now)
                        
                        # 如果成功获取变换，使用这个相机帧
                        posestamped.header.frame_id = camera_frame
                        camera_frame_used = camera_frame
                        found_transform = True
                        break
                    except Exception as e:
                        print("使用相机帧 {} 时发生错误: {}".format(camera_frame, str(e)))
                        continue
            
            # 如果在预定义的相机帧中都没找到，尝试使用任意可用的相机相关帧
            if not found_transform:
                print("尝试使用其他可能的相机帧...")
                camera_frames, optical_frames = detect_camera_frames(listener)
                all_camera_related_frames = list(set(camera_frames + optical_frames))
                
                for camera_frame in all_camera_related_frames:
                    if camera_frame in available_frames:
                        print("尝试使用检测到的相机帧: {}".format(camera_frame))
                        try:
                            now = rospy.Time.now()
                            listener.waitForTransform(camera_frame, tag_frame, now, rospy.Duration(1.0))
                            (trans, rot) = listener.lookupTransform(camera_frame, tag_frame, now)
                            
                            posestamped.header.frame_id = camera_frame
                            camera_frame_used = camera_frame
                            found_transform = True
                            break
                        except Exception as e:
                            print("使用相机帧 {} 时发生错误: {}".format(camera_frame, str(e)))
                            continue
            
            if not found_transform:
                print("在所有尝试的相机帧中都无法找到到Tag {}的变换".format(tag_id))
                # 显示当前可用的所有tag帧
                tag_frames = [f for f in available_frames if f.startswith('tag_')]
                if tag_frames:
                    print("当前可用的tag帧: {}".format(tag_frames))
                else:
                    print("当前没有检测到任何tag帧")
                raise Exception("无法找到合适的相机帧")
            
            # 存储AprilTag的姿态信息
            print("成功检测到Tag {} (使用相机帧: {}): {:.3f}, {:.3f}, {:.3f}, {:.3f}, {:.3f}, {:.3f}, {:.3f}".format(
                tag_id, camera_frame_used, trans[0], trans[1], trans[2], rot[0], rot[1], rot[2], rot[3]))
            
            posestamped.pose.position.x = trans[0]
            posestamped.pose.position.y = trans[1]
            posestamped.pose.position.z = trans[2]
            posestamped.pose.orientation.x = rot[0]
            posestamped.pose.orientation.y = rot[1]
            posestamped.pose.orientation.z = rot[2]
            posestamped.pose.orientation.w = rot[3]
            
            # 计算抓取方向（旋转180度）
            m1 = tfs.euler.euler2mat(math.radians(180), math.radians(0), math.radians(0))
            m2 = tfs.quaternions.quat2mat([rot[3], rot[0], rot[1], rot[2]])
            rot2 = tfs.quaternions.mat2quat(np.dot(m2, m1)[0:3, 0:3])
            
            # 广播坐标变换
            broadcaster = tf.TransformBroadcaster()
            t2 = TransformStamped()
            t2.header.frame_id = 'camera_color_optical_frame'
            t2.header.stamp = rospy.Time(0)
            t2.child_frame_id = 't2'
            t2.transform.translation = posestamped.pose.position
            t2.transform.rotation.w = rot2[0]
            t2.transform.rotation.x = rot2[1]
            t2.transform.rotation.y = rot2[2]
            t2.transform.rotation.z = rot2[3]
            t2.transform.rotation=posestamped.pose.orientation
            rate = rospy.Rate(1)
            broadcaster.sendTransformMessage(t2)
            
            get_pose = True
            
        except tf.LookupException as e:
            print("查找变换失败 (LookupException): {}".format(str(e)))
            print("可能的原因:")
            print("  1. AprilTag {} 未被检测到".format(tag_id))
            print("  2. TF树中不存在 {} 帧".format(tag_frame))
        except tf.ConnectivityException as e:
            print("连接性错误 (ConnectivityException): {}".format(str(e)))
            print("可能的原因:")
            print("  1. TF树中 {} 和相机帧之间没有连接".format(tag_frame))
        except tf.ExtrapolationException as e:
            print("外推错误 (ExtrapolationException): {}".format(str(e)))
            print("可能的原因:")
            print("  1. 请求的时间点没有可用的变换数据")
        except Exception as e:
            print("检测AprilTag时发生未知错误: {}".format(str(e)))
        
        if not get_pose and attempt_count < max_attempts:
            print("等待1秒后重试...")
            rospy.sleep(1.0)
    
    if not get_pose:
        print("经过{}次尝试后仍无法检测到Tag {}".format(max_attempts, tag_id))
        print("建议检查以下几点:")
        print("  1. AprilTag是否在相机视野范围内")
        print("  2. AprilTag的ID是否正确 (期望ID: {})".format(tag_id))
        print("  3. AprilTag检测节点是否正在运行")
        print("  4. 相机是否正常工作")
        print("  5. 是否存在光照问题")
        print("  6. 相机驱动是否正确配置")
    
    return posestamped if get_pose else None

def transform_to_base_frame(listener, posestamped):
    """
    将目标姿态从相机坐标系转换到机器人基座坐标系
    
    Args:
        listener: TF监听器
        posestamped: 目标在相机坐标系中的姿态
    
    Returns:
        pose_stamped_return: 目标在基座坐标系中的姿态
    """
    get_pose = False
    pose_stamped_return = None
    
    attempt_count = 0
    max_attempts = 5
    
    while not rospy.is_shutdown() and not get_pose and attempt_count < max_attempts:
        try:
            attempt_count += 1
            print("尝试坐标变换到基座坐标系 (第{}次尝试)".format(attempt_count))
            
            now = rospy.Time.now()
            listener.waitForTransform("/base", posestamped.header.frame_id, now, rospy.Duration(0.5))
            pose_stamped_return = listener.transformPose("/base", posestamped)
            print("成功变换到基座坐标系: ", pose_stamped_return)
            get_pose = True
        except tf.LookupException as e:
            print("坐标变换失败 (LookupException): {}".format(str(e)))
        except tf.ConnectivityException as e:
            print("坐标变换连接性错误 (ConnectivityException): {}".format(str(e)))
        except tf.ExtrapolationException as e:
            print("坐标变换外推错误 (ExtrapolationException): {}".format(str(e)))
        except Exception as e:
            print("坐标变换时发生未知错误: {}".format(str(e)))
        
        if not get_pose and attempt_count < max_attempts:
            print("等待1秒后重试坐标变换...")
            rospy.sleep(1.0)
    
    if not get_pose:
        print("经过{}次尝试后仍无法完成坐标变换".format(max_attempts))
    
    return pose_stamped_return

def grab_object(arm, target_pose, tag_id):
    """
    执行抓取动作
    
    Args:
        arm: 机械臂控制对象
        target_pose: 目标姿态
        tag_id: AprilTag ID，用于获取对应的偏移量
    """
    # 获取该物块的偏移量配置
    offsets = GRAB_OFFSETS.get(tag_id, GRAB_OFFSETS[1])  # 默认使用第一个物块的配置
    
    # 设置目标姿态并进行微调
    target_pose.header.stamp = rospy.Time.now()
    arm.set_start_state_to_current_state()
    
    rospy.sleep(1)
    
    # 移动到目标位置上方（使用该物块特定的偏移量）
    above_offset = offsets["above"]
    print("移动到目标上方，使用偏移量: x={}, y={}, z={}".format(above_offset[0], above_offset[1], above_offset[2]))
    target_pose.pose.position.x = target_pose.pose.position.x + above_offset[0]
    target_pose.pose.position.y = target_pose.pose.position.y + above_offset[1]
    target_pose.pose.position.z = target_pose.pose.position.z + above_offset[2]
    arm.set_pose_target(target_pose)
    print("正在移动到目标上方...")
    arm.go(wait=True)
    
    # 向下移动抓取物体（使用该物块特定的偏移量）
    grab_offset = offsets["grab"]
    print("向下抓取，使用偏移量: x={}, y={}, z={}".format(grab_offset[0], grab_offset[1], grab_offset[2]))
    target_pose.pose.position.x = target_pose.pose.position.x + grab_offset[0]
    target_pose.pose.position.y = target_pose.pose.position.y + grab_offset[1]
    target_pose.pose.position.z = target_pose.pose.position.z + grab_offset[2]
    arm.set_pose_target(target_pose)
    print("正在向下抓取...")
    arm.go(wait=True)
    
    # 控制气泵执行抓取
    print("正在打开气泵执行抓取...")
    rospy.wait_for_service("switch_pump_status")
    pump_s = rospy.ServiceProxy("switch_pump_status", mirobotPump)
    pump_s(True)  # 打开气泵进行抓取
    
    # 等待抓取完成
    rospy.sleep(1)

def release_object(arm):
    """
    释放抓取的物体
    
    Args:
        arm: 机械臂控制对象
    """
    print("回到初始位置...")
    # 回到初始位置
    joint_position = [0, 0, 0, 0, 0, 0]
    arm.set_joint_value_target(joint_position)
    arm.go(wait=True)
    
    # 关闭气泵释放物体
    print("关闭气泵释放物体...")
    rospy.wait_for_service("switch_pump_status")
    pump_s = rospy.ServiceProxy("switch_pump_status", mirobotPump)
    pump_s(False)
    
    # 等待释放完成
    rospy.sleep(1)

def grab_multiple_objects(arm, listener, tag_ids):
    """
    连续抓取多个物体
    
    Args:
        arm: 机械臂控制对象
        listener: TF监听器
        tag_ids: AprilTag ID列表
    """
    for i, tag_id in enumerate(tag_ids):
        print("\n=== 开始抓取第{}个物体 (Tag {}) ===".format(i+1, tag_id))
        
        # 检测AprilTag
        pose_stamped = detect_apriltag(listener, tag_id)
        
        if pose_stamped is None:
            print("跳过Tag {}的抓取".format(tag_id))
            continue
        
        # 坐标系转换
        print("开始坐标系转换...")
        target_pose = transform_to_base_frame(listener, pose_stamped)
        
        # 执行抓取
        if target_pose:
            print("开始执行抓取动作...")
            grab_object(arm, target_pose, tag_id)
            print("第{}个物体抓取完成".format(i+1))
            
            # 释放物体
            print("开始释放物体...")
            release_object(arm)
            print("第{}个物体释放完成".format(i+1))
        else:
            print("无法获取第{}个物体的正确姿态，跳过".format(i+1))
        
        # 等待一段时间再抓取下一个物体
        if i < len(tag_ids) - 1:
            print("等待2秒后继续抓取下一个物体...")
            rospy.sleep(2)
    
    print("所有物体抓取任务完成")

def main():
    """
    主函数
    """
    try:
        # 初始化系统
        print("正在初始化系统...")
        arm, listener = initialize_system()
        print("系统初始化完成")
        
        # 定义要抓取的三个物块的AprilTag ID
        tag_ids = [1, 2, 3]  # 可根据实际情况修改
        print("准备抓取的Tag ID列表: {}".format(tag_ids))
        
        # 执行连续抓取任务
        grab_multiple_objects(arm, listener, tag_ids)
        
    except rospy.ROSInterruptException:
        print("程序被中断")
    except Exception as e:
        print("发生错误: ", str(e))
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()