#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试图像发布器
用于测试火灾检测服务，发布测试图像
"""

import rospy
import cv2
import numpy as np
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import os

class ImagePublisherTest:
    def __init__(self):
        """初始化图像发布器"""
        rospy.init_node('image_publisher_test', anonymous=True)
        
        # 初始化CV Bridge
        self.bridge = CvBridge()
        
        # 创建图像发布器
        self.image_pub = rospy.Publisher('/camera/image_raw', Image, queue_size=1)
        
        # 发布频率
        self.rate = rospy.Rate(10)  # 10Hz
        
        rospy.loginfo("测试图像发布器已启动")
    
    def create_test_building_image(self):
        """创建测试楼宇图像"""
        # 创建白色背景
        img = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # 绘制楼宇轮廓
        building_color = (139, 69, 19)  # 棕色
        cv2.rectangle(img, (200, 100), (440, 400), building_color, -1)
        
        # 绘制楼层分界线
        for i in range(1, 5):
            y = 100 + i * 60
            cv2.line(img, (200, y), (440, y), (0, 0, 0), 2)
        
        # 绘制窗户
        window_color = (173, 216, 230)  # 浅蓝色
        for floor in range(5):
            for window in range(4):
                x1 = 220 + window * 50
                y1 = 110 + floor * 60
                x2 = x1 + 30
                y2 = y1 + 40
                cv2.rectangle(img, (x1, y1), (x2, y2), window_color, -1)
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 0), 1)
        
        # 添加楼宇名称（中文）
        font = cv2.FONT_HERSHEY_SIMPLEX
        # 注意：OpenCV对中文支持有限，这里用英文代替，实际使用时需要PIL或其他库
        cv2.putText(img, "Electronic Building", (180, 80), font, 0.8, (0, 0, 0), 2)
        
        # 添加火焰（在第2层）
        fire_center = (270, 230)  # 第2层位置
        fire_color = (0, 69, 255)  # 红色火焰
        cv2.circle(img, fire_center, 15, fire_color, -1)
        
        # 添加火焰效果
        for i in range(5):
            offset_x = np.random.randint(-10, 10)
            offset_y = np.random.randint(-5, 5)
            fire_pos = (fire_center[0] + offset_x, fire_center[1] + offset_y)
            radius = np.random.randint(5, 12)
            cv2.circle(img, fire_pos, radius, (0, 100, 255), -1)
        
        return img
    
    def create_normal_building_image(self):
        """创建正常楼宇图像（无火灾）"""
        # 创建白色背景
        img = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # 绘制楼宇轮廓
        building_color = (139, 69, 19)  # 棕色
        cv2.rectangle(img, (200, 100), (440, 400), building_color, -1)
        
        # 绘制楼层分界线
        for i in range(1, 5):
            y = 100 + i * 60
            cv2.line(img, (200, y), (440, y), (0, 0, 0), 2)
        
        # 绘制窗户
        window_color = (173, 216, 230)  # 浅蓝色
        for floor in range(5):
            for window in range(4):
                x1 = 220 + window * 50
                y1 = 110 + floor * 60
                x2 = x1 + 30
                y2 = y1 + 40
                cv2.rectangle(img, (x1, y1), (x2, y2), window_color, -1)
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 0), 1)
        
        # 添加楼宇名称
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(img, "Office Building", (180, 80), font, 0.8, (0, 0, 0), 2)
        
        return img
    
    def publish_test_images(self):
        """发布测试图像"""
        counter = 0
        
        while not rospy.is_shutdown():
            # 交替发布有火灾和无火灾的图像
            if counter % 20 < 10:  # 前10帧发布有火灾的图像
                img = self.create_test_building_image()
                rospy.loginfo("发布火灾测试图像")
            else:  # 后10帧发布正常图像
                img = self.create_normal_building_image()
                rospy.loginfo("发布正常测试图像")
            
            # 转换为ROS图像消息
            try:
                img_msg = self.bridge.cv2_to_imgmsg(img, "bgr8")
                img_msg.header.stamp = rospy.Time.now()
                img_msg.header.frame_id = "camera"
                
                # 发布图像
                self.image_pub.publish(img_msg)
                
            except Exception as e:
                rospy.logerr(f"图像发布失败: {e}")
            
            counter += 1
            self.rate.sleep()
    
    def publish_single_fire_image(self):
        """发布单张火灾图像"""
        img = self.create_test_building_image()
        
        try:
            img_msg = self.bridge.cv2_to_imgmsg(img, "bgr8")
            img_msg.header.stamp = rospy.Time.now()
            img_msg.header.frame_id = "camera"
            
            # 持续发布同一张图像
            while not rospy.is_shutdown():
                self.image_pub.publish(img_msg)
                rospy.loginfo("发布火灾测试图像")
                self.rate.sleep()
                
        except Exception as e:
            rospy.logerr(f"图像发布失败: {e}")

def main():
    """主函数"""
    try:
        publisher = ImagePublisherTest()
        
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == 'single':
            # 发布单张火灾图像
            publisher.publish_single_fire_image()
        else:
            # 交替发布测试图像
            publisher.publish_test_images()
            
    except rospy.ROSInterruptException:
        rospy.loginfo("图像发布器已停止")

if __name__ == '__main__':
    main()
