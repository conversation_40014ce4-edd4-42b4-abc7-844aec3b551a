#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
火焰检测可视化测试程序
能够加载图片，检测火焰位置，并在图片上标出火焰区域
"""

import cv2
import numpy as np
import os
import argparse
from paddleocr import PaddleOCR
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.font_manager import FontProperties
from ultralytics import YOLO

class FireDetectionVisualTest:
    def __init__(self, model_path="model/fire.pt"):
        """初始化火焰检测可视化测试"""
        # 初始化PaddleOCR
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch')

        # 加载YOLOv8火焰检测模型
        try:
            self.fire_model = YOLO(model_path)
            print(f"成功加载YOLOv8火焰检测模型: {model_path}")
        except Exception as e:
            print(f"加载模型失败: {e}")
            self.fire_model = None

        # 检测置信度阈值
        self.confidence_threshold = 0.5

        print("火焰检测可视化测试程序已初始化")
    
    def detect_building_name(self, image):
        """使用PaddleOCR检测楼宇名称"""
        try:
            result = self.ocr.ocr(image, cls=True)
            
            building_name = ""
            max_confidence = 0
            text_boxes = []
            
            if result and result[0]:
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    box = line[0]
                    
                    text_boxes.append({
                        'text': text,
                        'confidence': confidence,
                        'box': box
                    })
                    
                    # 查找包含楼宇相关关键词的文本
                    if any(keyword in text for keyword in ['楼', '大厦', '中心', '广场', '大楼', '建筑']):
                        if confidence > max_confidence:
                            max_confidence = confidence
                            building_name = text
                    
                    # 如果没有找到楼宇关键词，选择置信度最高的文本作为楼宇名称
                    if not building_name and confidence > max_confidence:
                        max_confidence = confidence
                        building_name = text
            
            return building_name if building_name else "未知楼宇", text_boxes
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return "未知楼宇", []
    
    def detect_fire_regions(self, image):
        """使用YOLOv8模型检测所有火焰区域"""
        try:
            if self.fire_model is None:
                print("火焰检测模型未加载")
                return [], None

            # 使用YOLOv8模型进行推理
            results = self.fire_model(image, conf=self.confidence_threshold)

            fire_regions = []
            fire_mask = np.zeros(image.shape[:2], dtype=np.uint8)

            # 处理检测结果
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)

                        # 计算宽度、高度和中心点
                        w = x2 - x1
                        h = y2 - y1
                        cx = x1 + w // 2
                        cy = y1 + h // 2

                        # 获取置信度
                        confidence = float(box.conf[0].cpu().numpy())

                        # 获取类别（如果有多个类别）
                        class_id = int(box.cls[0].cpu().numpy()) if box.cls is not None else 0

                        # 计算面积
                        area = w * h

                        # 在掩码上绘制检测区域
                        cv2.rectangle(fire_mask, (x1, y1), (x2, y2), 255, -1)

                        fire_regions.append({
                            'bbox': (x1, y1, w, h),
                            'center': (cx, cy),
                            'area': area,
                            'confidence': confidence,
                            'class_id': class_id
                        })

            return fire_regions, fire_mask

        except Exception as e:
            print(f"YOLOv8火焰检测失败: {e}")
            return [], None
    
    def determine_floor_from_position(self, fire_center, image_height, floors=5):
        """根据火焰位置确定楼层"""
        if fire_center is None:
            return None
        
        cx, cy = fire_center
        
        # 将图像高度分为若干层
        floor_height = image_height / floors
        
        # 计算火焰所在楼层（从下往上数）
        floor_number = floors - int(cy / floor_height)
        floor_number = max(1, min(floors, floor_number))
        
        return floor_number
    
    def visualize_detection_results(self, image, building_name, text_boxes, fire_regions, save_path=None):
        """可视化检测结果"""
        # 创建图像副本用于绘制
        result_image = image.copy()
        
        # 在图像上绘制火焰区域
        for i, fire_region in enumerate(fire_regions):
            # 绘制边界框
            x, y, w, h = fire_region['bbox']
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 0, 255), 3)

            # 绘制中心点
            cx, cy = fire_region['center']
            cv2.circle(result_image, (cx, cy), 5, (255, 0, 0), -1)

            # 计算楼层
            floor = self.determine_floor_from_position(fire_region['center'], image.shape[0])

            # 获取置信度
            confidence = fire_region.get('confidence', 0.0)

            # 添加标签
            label = f"Fire {i+1}: {floor}F ({confidence:.2f})"
            cv2.putText(result_image, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # 绘制OCR识别的文本框
        for text_box in text_boxes:
            box = np.array(text_box['box'], dtype=np.int32)
            cv2.polylines(result_image, [box], True, (0, 255, 0), 2)
            
            # 添加文本标签
            text = text_box['text']
            confidence = text_box['confidence']
            label = f"{text} ({confidence:.2f})"
            cv2.putText(result_image, label, tuple(box[0]), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 使用matplotlib显示结果
        plt.figure(figsize=(15, 10))
        
        # 原图
        plt.subplot(2, 2, 1)
        plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        plt.title('原始图像')
        plt.axis('off')
        
        # 检测结果
        plt.subplot(2, 2, 2)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title('检测结果')
        plt.axis('off')
        
        # 火焰掩码
        if len(fire_regions) > 0:
            _, fire_mask = self.detect_fire_regions(image)
            plt.subplot(2, 2, 3)
            plt.imshow(fire_mask, cmap='gray')
            plt.title('火焰掩码')
            plt.axis('off')
        
        # 检测信息
        plt.subplot(2, 2, 4)
        plt.axis('off')
        
        # 生成检测报告
        report = f"楼宇名称: {building_name}\n\n"
        if fire_regions:
            report += f"检测到 {len(fire_regions)} 处火焰:\n"
            for i, fire_region in enumerate(fire_regions):
                floor = self.determine_floor_from_position(fire_region['center'], image.shape[0])
                area = fire_region['area']
                report += f"火焰 {i+1}: {floor}楼, 面积: {area:.0f}像素\n"
            
            # 生成最终结果
            main_fire = max(fire_regions, key=lambda x: x['area'])
            main_floor = self.determine_floor_from_position(main_fire['center'], image.shape[0])
            report += f"\n最终结果: {building_name} {main_floor}楼发现火灾"
        else:
            report += "未检测到火焰\n"
            report += f"最终结果: {building_name} 未发现火灾"
        
        plt.text(0.1, 0.9, report, transform=plt.gca().transAxes, fontsize=12, 
                verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果已保存到: {save_path}")
        
        plt.show()
        
        return result_image, report
    
    def test_image(self, image_path, save_result=True):
        """测试单张图像"""
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            return
        
        # 加载图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法加载图像: {image_path}")
            return
        
        print(f"正在处理图像: {image_path}")
        print(f"图像尺寸: {image.shape}")
        
        # 检测楼宇名称
        building_name, text_boxes = self.detect_building_name(image)
        print(f"识别到楼宇名称: {building_name}")
        
        # 检测火焰区域
        fire_regions, _ = self.detect_fire_regions(image)
        print(f"检测到 {len(fire_regions)} 处火焰区域")
        
        # 可视化结果
        save_path = None
        if save_result:
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            save_path = f"{base_name}_fire_detection_result.png"
        
        result_image, report = self.visualize_detection_results(
            image, building_name, text_boxes, fire_regions, save_path
        )
        
        print("\n" + "="*50)
        print("检测报告:")
        print("="*50)
        print(report)
        print("="*50)
        
        return result_image, report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='火焰检测可视化测试')
    parser.add_argument('image_path', help='输入图像路径')
    parser.add_argument('--no-save', action='store_true', help='不保存结果图像')
    
    args = parser.parse_args()
    
    # 创建测试实例
    tester = FireDetectionVisualTest()
    
    # 测试图像
    tester.test_image(args.image_path, save_result=not args.no_save)

if __name__ == '__main__':
    # 如果没有命令行参数，提供交互式输入
    import sys
    if len(sys.argv) == 1:
        print("火焰检测可视化测试程序")
        print("请输入图像路径，或输入 'demo' 创建演示图像:")
        
        image_path = input("图像路径: ").strip()
        
        if image_path.lower() == 'demo':
            # 创建演示图像
            demo_image = np.ones((480, 640, 3), dtype=np.uint8) * 255
            
            # 绘制楼宇
            building_color = (139, 69, 19)
            cv2.rectangle(demo_image, (200, 100), (440, 400), building_color, -1)
            
            # 绘制楼层线
            for i in range(1, 5):
                y = 100 + i * 60
                cv2.line(demo_image, (200, y), (440, y), (0, 0, 0), 2)
            
            # 绘制窗户
            window_color = (173, 216, 230)
            for floor in range(5):
                for window in range(4):
                    x1 = 220 + window * 50
                    y1 = 110 + floor * 60
                    x2 = x1 + 30
                    y2 = y1 + 40
                    cv2.rectangle(demo_image, (x1, y1), (x2, y2), window_color, -1)
            
            # 添加火焰
            fire_center = (270, 230)
            cv2.circle(demo_image, fire_center, 20, (0, 69, 255), -1)
            cv2.circle(demo_image, (280, 225), 15, (0, 100, 255), -1)
            
            # 保存演示图像
            demo_path = "demo_building_fire.jpg"
            cv2.imwrite(demo_path, demo_image)
            print(f"演示图像已创建: {demo_path}")
            image_path = demo_path
        
        if os.path.exists(image_path):
            tester = FireDetectionVisualTest()
            tester.test_image(image_path)
        else:
            print(f"图像文件不存在: {image_path}")
    else:
        main()
