# 楼层校准工具使用说明

## 问题描述
当火焰检测正确但楼层识别不准确时，可以使用楼层校准工具来手动标定楼层位置。

## 使用步骤

### 1. 运行楼层校准工具
```bash
cd artificial_intelligence/ROS/fire_detection
python floor_calibration_tool.py
```

### 2. 输入图像路径
程序会提示输入图像路径，输入您要校准的图像文件路径。

### 3. 手动标记楼层分界线
- **左键点击**: 在楼层分界线位置点击，添加楼层分界线
- **右键点击**: 删除最后添加的楼层线
- **按 'R' 键**: 重置所有标记
- **按 'A' 键**: 重新自动检测楼层线
- **按 'S' 键**: 保存校准数据
- **按 'Q' 键**: 退出校准

### 4. 标记要点
- 从上到下依次标记每个楼层的分界线
- 通常标记楼层之间的水平分界线（如楼板、窗台等）
- 确保标记的线条能够清晰区分不同楼层

### 5. 保存校准数据
按 'S' 键保存校准数据，会生成两个文件：
- `图像名_floor_calibration.json`: 校准数据文件
- `图像名_floor_marked.jpg`: 标记了楼层线的图像

### 6. 使用校准数据
保存校准数据后，再次运行火焰检测程序时，会自动加载同名的校准数据文件，提高楼层检测准确性。

```bash
python yolo_fire_test.py
```

## 校准数据格式
校准数据以JSON格式保存，包含以下信息：
```json
{
  "image_path": "图像路径",
  "image_size": [宽度, 高度],
  "floor_lines": [楼层线Y坐标列表],
  "floor_count": 楼层总数
}
```

## 注意事项
1. 校准数据文件名必须与图像文件名匹配（除了扩展名）
2. 楼层线应该从上到下按顺序标记
3. 标记的楼层线越准确，火焰楼层检测越精确
4. 可以多次校准同一张图像，新的校准数据会覆盖旧的

## 示例工作流程
1. 发现火焰检测正确但楼层错误
2. 运行 `python floor_calibration_tool.py`
3. 输入图像路径
4. 手动标记楼层分界线
5. 按 'S' 保存校准数据
6. 重新运行 `python yolo_fire_test.py` 测试同一图像
7. 验证楼层检测是否准确

## 故障排除
- 如果自动检测的楼层线不准确，可以按 'R' 重置后手动标记
- 如果校准数据加载失败，检查JSON文件格式是否正确
- 如果楼层检测仍不准确，可以重新校准或调整标记位置
